'use client'

import React, { useState, useEffect } from 'react'
import {
  Globe,
  CreditCard,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Zap,
  Shield,
  Users,
  Package,
  TrendingUp,
  Settings,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface Subscription {
  id: string
  planName: string
  planType: 'BASIC' | 'PROFESSIONAL' | 'ENTERPRISE'
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'TRIAL'
  startDate: string
  endDate: string
  nextBillingDate: string
  amount: number
  billingCycle: 'MONTHLY' | 'YEARLY'
  features: string[]
  usage: {
    stores: { used: number; limit: number }
    users: { used: number; limit: number }
    products: { used: number; limit: number }
    storage: { used: number; limit: number }
  }
}

interface Plan {
  id: string
  name: string
  type: 'BASIC' | 'PROFESSIONAL' | 'ENTERPRISE'
  description: string
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  limits: {
    stores: number
    users: number
    products: number
    storage: number
  }
  popular?: boolean
}

export default function SubscriptionsPage() {
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [billingCycle, setBillingCycle] = useState<'MONTHLY' | 'YEARLY'>('MONTHLY')

  useEffect(() => {
    fetchSubscriptionData()
  }, [])

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true)
      const [subscriptionResponse, plansResponse] = await Promise.all([
        fetch('/api/subscriptions'),
        fetch('/api/subscriptions/plans')
      ])

      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json()
        setSubscription(subscriptionData.data)
      }

      if (plansResponse.ok) {
        const plansData = await plansResponse.json()
        setPlans(plansData.data || [])
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'TRIAL': return 'bg-blue-100 text-blue-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'EXPIRED': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'TRIAL': return <Clock className="w-4 h-4 text-blue-600" />
      case 'CANCELLED': return <XCircle className="w-4 h-4 text-red-600" />
      case 'EXPIRED': return <AlertTriangle className="w-4 h-4 text-gray-600" />
      default: return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'BASIC': return <Package className="w-6 h-6 text-blue-600" />
      case 'PROFESSIONAL': return <Zap className="w-6 h-6 text-purple-600" />
      case 'ENTERPRISE': return <Shield className="w-6 h-6 text-gold-600" />
      default: return <Package className="w-6 h-6 text-gray-600" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const calculateUsagePercentage = (used: number, limit: number) => {
    return limit === -1 ? 0 : Math.min((used / limit) * 100, 100)
  }

  const formatStorage = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024)
    return `${gb.toFixed(1)} GB`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Subscription Management</h1>
          <p className="text-gray-600 mt-1">Manage your subscription plan and billing</p>
        </div>
        <Button variant="outline" size="sm" onClick={fetchSubscriptionData}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="current" className="space-y-6">
        <TabsList>
          <TabsTrigger value="current">Current Subscription</TabsTrigger>
          <TabsTrigger value="plans">Available Plans</TabsTrigger>
          <TabsTrigger value="billing">Billing History</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-6">
          {subscription ? (
            <>
              {/* Current Plan Overview */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getPlanIcon(subscription.planType)}
                      <div>
                        <CardTitle className="text-xl">{subscription.planName}</CardTitle>
                        <p className="text-gray-600">
                          {subscription.billingCycle === 'MONTHLY' ? 'Monthly' : 'Yearly'} Plan
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(subscription.status)}
                      <Badge className={getStatusColor(subscription.status)}>
                        {subscription.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Current Period</p>
                      <p className="text-lg font-semibold">
                        {new Date(subscription.startDate).toLocaleDateString()} - {new Date(subscription.endDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Next Billing</p>
                      <p className="text-lg font-semibold">
                        {new Date(subscription.nextBillingDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Amount</p>
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrency(subscription.amount)}
                      </p>
                    </div>
                  </div>

                  <div className="mt-6 flex gap-4">
                    <Button>
                      <Settings className="w-4 h-4 mr-2" />
                      Manage Subscription
                    </Button>
                    <Button variant="outline">
                      <CreditCard className="w-4 h-4 mr-2" />
                      Update Payment Method
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Usage Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle>Usage Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Stores</span>
                        <span className="text-sm text-gray-500">
                          {subscription.usage.stores.used} / {subscription.usage.stores.limit === -1 ? '∞' : subscription.usage.stores.limit}
                        </span>
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(subscription.usage.stores.used, subscription.usage.stores.limit)} 
                        className="h-2"
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Users</span>
                        <span className="text-sm text-gray-500">
                          {subscription.usage.users.used} / {subscription.usage.users.limit === -1 ? '∞' : subscription.usage.users.limit}
                        </span>
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(subscription.usage.users.used, subscription.usage.users.limit)} 
                        className="h-2"
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Products</span>
                        <span className="text-sm text-gray-500">
                          {subscription.usage.products.used.toLocaleString()} / {subscription.usage.products.limit === -1 ? '∞' : subscription.usage.products.limit.toLocaleString()}
                        </span>
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(subscription.usage.products.used, subscription.usage.products.limit)} 
                        className="h-2"
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Storage</span>
                        <span className="text-sm text-gray-500">
                          {formatStorage(subscription.usage.storage.used)} / {subscription.usage.storage.limit === -1 ? '∞' : formatStorage(subscription.usage.storage.limit)}
                        </span>
                      </div>
                      <Progress 
                        value={calculateUsagePercentage(subscription.usage.storage.used, subscription.usage.storage.limit)} 
                        className="h-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Features */}
              <Card>
                <CardHeader>
                  <CardTitle>Included Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {subscription.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Globe className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                <p className="text-gray-500 mb-4">Choose a plan to get started with our services</p>
                <Button>View Available Plans</Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="plans" className="space-y-6">
          {/* Billing Cycle Toggle */}
          <div className="flex justify-center">
            <div className="flex items-center space-x-4 bg-gray-100 p-1 rounded-lg">
              <Button
                variant={billingCycle === 'MONTHLY' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setBillingCycle('MONTHLY')}
              >
                Monthly
              </Button>
              <Button
                variant={billingCycle === 'YEARLY' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setBillingCycle('YEARLY')}
              >
                Yearly
                <Badge variant="secondary" className="ml-2">Save 20%</Badge>
              </Button>
            </div>
          </div>

          {/* Plans Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <Card key={plan.id} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white">
                      <Star className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-4">
                    {getPlanIcon(plan.type)}
                  </div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <p className="text-gray-600">{plan.description}</p>
                  <div className="mt-4">
                    <span className="text-3xl font-bold">
                      {formatCurrency(billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice)}
                    </span>
                    <span className="text-gray-500">
                      /{billingCycle === 'MONTHLY' ? 'month' : 'year'}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="space-y-2 mb-6 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Stores:</span>
                      <span>{plan.limits.stores === -1 ? 'Unlimited' : plan.limits.stores}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Users:</span>
                      <span>{plan.limits.users === -1 ? 'Unlimited' : plan.limits.users}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Products:</span>
                      <span>{plan.limits.products === -1 ? 'Unlimited' : plan.limits.products.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Storage:</span>
                      <span>{plan.limits.storage === -1 ? 'Unlimited' : formatStorage(plan.limits.storage)}</span>
                    </div>
                  </div>

                  <Button 
                    className="w-full" 
                    variant={plan.popular ? 'default' : 'outline'}
                    disabled={subscription?.planType === plan.type}
                  >
                    {subscription?.planType === plan.type ? 'Current Plan' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <CreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No billing history</h3>
                <p className="text-gray-500">Your billing history will appear here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
