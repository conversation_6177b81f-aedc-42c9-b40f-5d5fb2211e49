'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { DashboardStats, SalesData } from '@/lib/types'
import { SalesChart } from '@/components/dashboard/sales-chart'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Bell,
  RefreshCw,
  TrendingUp,
  Users,
  Package,
  ShoppingCart,
  DollarSign,
  Store,
  AlertTriangle,
  Shield,
  Activity,
  BarChart3,
  PieChart,
  CreditCard,
  Truck,
  FileText,
  Settings,
  Eye,
  Lock,
  Calendar
} from 'lucide-react'
import { toast } from 'sonner'
import { hasPermission } from '@/lib/permissions'
import { Role } from '@/lib/types'

interface RoleSpecificData {
  [key: string]: any
}

export default function DashboardPage() {
  const { user, token } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [salesData, setSalesData] = useState<SalesData[]>([])
  const [roleData, setRoleData] = useState<RoleSpecificData>({})
  const [isLoading, setIsLoading] = useState(true)

  const userRole = user?.role as Role || 'STAFF'

  const fetchDashboardData = async () => {
    if (!token) return

    try {
      setIsLoading(true)

      // Fetch role-specific data based on permissions
      const endpoints = [
        '/api/dashboard/stats',
        '/api/dashboard/sales-chart'
      ]

      // Add role-specific endpoints
      if (hasPermission(userRole, 'ANALYTICS', 'READ')) {
        endpoints.push('/api/analytics')
      }
      if (hasPermission(userRole, 'SECURITY', 'READ')) {
        endpoints.push('/api/security/stats')
      }
      if (hasPermission(userRole, 'FINANCIAL', 'READ')) {
        endpoints.push('/api/financial/summary')
      }
      if (hasPermission(userRole, 'USER', 'READ')) {
        endpoints.push('/api/users/stats')
      }

      const responses = await Promise.all(
        endpoints.map(endpoint => 
          fetch(endpoint, {
            headers: { Authorization: `Bearer ${token}` }
          }).catch(() => null)
        )
      )

      // Process basic stats
      if (responses[0]?.ok) {
        const statsData = await responses[0].json()
        setStats(statsData)
      }

      // Process sales chart
      if (responses[1]?.ok) {
        const chartData = await responses[1].json()
        setSalesData(chartData)
      }

      // Process role-specific data
      const roleSpecificData: RoleSpecificData = {}
      let responseIndex = 2

      if (hasPermission(userRole, 'ANALYTICS', 'READ') && responses[responseIndex]?.ok) {
        roleSpecificData.analytics = await responses[responseIndex].json()
        responseIndex++
      }
      if (hasPermission(userRole, 'SECURITY', 'READ') && responses[responseIndex]?.ok) {
        roleSpecificData.security = await responses[responseIndex].json()
        responseIndex++
      }
      if (hasPermission(userRole, 'FINANCIAL', 'READ') && responses[responseIndex]?.ok) {
        roleSpecificData.financial = await responses[responseIndex].json()
        responseIndex++
      }
      if (hasPermission(userRole, 'USER', 'READ') && responses[responseIndex]?.ok) {
        roleSpecificData.users = await responses[responseIndex].json()
      }

      setRoleData(roleSpecificData)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [token, userRole])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3">Loading {userRole.toLowerCase().replace('_', ' ')} dashboard...</span>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {userRole === 'FOUNDER' ? 'Executive Dashboard' :
             userRole === 'SUPER_ADMIN' ? 'Administrative Dashboard' :
             userRole === 'ADMIN' ? 'Operations Dashboard' :
             userRole === 'STAFF' ? 'Staff Dashboard' :
             'Partner Dashboard'}
          </h1>
          <p className="text-gray-600 mt-1">
            Welcome back, {user?.name}! Here's your {userRole.toLowerCase().replace('_', ' ')} overview.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">{userRole.replace('_', ' ')}</Badge>
          <Button variant="outline" size="sm" onClick={fetchDashboardData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {hasPermission(userRole, 'NOTIFICATION', 'READ') && (
            <Button variant="outline" size="sm">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
              {stats && stats.lowStockItems > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {stats.lowStockItems}
                </Badge>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Role-Specific Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Basic Stats for all roles */}
        {stats && (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₹{stats.totalSales?.toLocaleString() || '0'}</div>
                {stats.salesGrowth && (
                  <p className="text-xs text-muted-foreground">
                    <TrendingUp className="h-3 w-3 inline mr-1" />
                    +{stats.salesGrowth}% from last month
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalProducts?.toLocaleString() || '0'}</div>
                <p className="text-xs text-muted-foreground">
                  Active inventory items
                </p>
              </CardContent>
            </Card>

            {/* Role-specific cards */}
            {hasPermission(userRole, 'FINANCIAL', 'READ') && roleData.financial && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹{roleData.financial.totalRevenue?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    +{roleData.financial.revenueGrowth || 0}% from last month
                  </p>
                </CardContent>
              </Card>
            )}

            {hasPermission(userRole, 'USER', 'MANAGE') && roleData.users && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{roleData.users.totalUsers?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    Active system users
                  </p>
                </CardContent>
              </Card>
            )}

            {hasPermission(userRole, 'SECURITY', 'READ') && roleData.security && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Security Events</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{roleData.security.todayEvents?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    Today's security events
                  </p>
                </CardContent>
              </Card>
            )}

            {hasPermission(userRole, 'STORE', 'READ') && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Stores</CardTitle>
                  <Store className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalStores?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    Active store locations
                  </p>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>

      {/* Main Chart and Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              {userRole === 'FOUNDER' || userRole === 'SUPER_ADMIN' ? 'Executive Analytics' :
               userRole === 'ADMIN' ? 'Operations Overview' : 'Daily Activity'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <SalesChart data={salesData} />
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {userRole === 'FOUNDER' && (
              <>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Shield className="h-4 w-4 mr-2" />
                  Security Center
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Financial Reports
                </Button>
              </>
            )}
            {userRole === 'SUPER_ADMIN' && (
              <>
                <Button className="w-full justify-start" variant="outline">
                  <Store className="h-4 w-4 mr-2" />
                  Manage Stores
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  Inventory Overview
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </Button>
              </>
            )}
            {userRole === 'ADMIN' && (
              <>
                <Button className="w-full justify-start" variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  Add Product
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Truck className="h-4 w-4 mr-2" />
                  Purchase Orders
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Customer Management
                </Button>
              </>
            )}
            {userRole === 'STAFF' && (
              <>
                <Button className="w-full justify-start" variant="outline">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  New Sale
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Add Customer
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  View Inventory
                </Button>
              </>
            )}
            {userRole === 'DISTRIBUTOR' && (
              <>
                <Button className="w-full justify-start" variant="outline">
                  <Truck className="h-4 w-4 mr-2" />
                  View Orders
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  Approve Returns
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
