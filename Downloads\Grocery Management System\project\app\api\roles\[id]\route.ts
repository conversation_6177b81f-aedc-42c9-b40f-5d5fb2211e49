import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { roleStorage, predefinedRoles, CustomRole } from '@/lib/role-storage'
import { withPermission, createSuccessResponse, createErrorResponse } from '@/lib/api-utils'

// Schema for role updates
const updateRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').optional(),
  description: z.string().optional(),
  modules: z.array(z.string()).optional(),
  permissions: z.record(z.array(z.string())).optional()
})

// GET /api/roles/[id] - Get single role
export const GET = withPermission('SETTINGS', 'READ', async (
  request: NextRequest,
  user: any,
  context?: { params: { id: string } }
) => {
  const id = context?.params?.id

  if (!id) {
    return createErrorResponse('Role ID is required', 400)
  }

  // Find in predefined roles
  const predefinedRole = predefinedRoles.find(role => role.id === id)
  if (predefinedRole) {
    return createSuccessResponse(predefinedRole, 'Role fetched successfully')
  }

  // Find in custom roles
  const customRole = roleStorage.getById(id)
  if (customRole) {
    return createSuccessResponse(customRole, 'Role fetched successfully')
  }

  return createErrorResponse('Role not found', 404)
})

// PUT /api/roles/[id] - Update role
export const PUT = withPermission('SETTINGS', 'UPDATE', async (
  request: NextRequest,
  user: any,
  context?: { params: { id: string } }
) => {
  try {
    const id = context?.params?.id

    if (!id) {
      return createErrorResponse('Role ID is required', 400)
    }

    const body = await request.json()
    const updateData = updateRoleSchema.parse(body)

    // Prevent modification of predefined roles
    const predefinedRoleIds = ['founder', 'super_admin', 'admin', 'staff', 'distributor']
    if (predefinedRoleIds.includes(id)) {
      return createErrorResponse('Cannot modify predefined roles', 400)
    }

    // Prepare update data with calculated fields
    const updateWithCalculatedFields = {
      ...updateData,
      moduleCount: updateData.modules ? updateData.modules.length : undefined,
      permissionCount: updateData.permissions
        ? Object.values(updateData.permissions).reduce((acc: number, perms: any) => acc + perms.length, 0)
        : undefined,
      updatedAt: new Date().toISOString()
    }

    // Update the role
    const updatedRole = roleStorage.update(id, updateWithCalculatedFields)

    if (!updatedRole) {
      return createErrorResponse('Role not found', 404)
    }

    console.log('Updated role:', updatedRole)

    return createSuccessResponse(updatedRole, 'Role updated successfully')
  } catch (error) {
    console.error('Error updating role:', error)
    if (error instanceof z.ZodError) {
      return createErrorResponse(`Invalid role data: ${error.errors.map(e => e.message).join(', ')}`, 400)
    }
    return createErrorResponse('Failed to update role', 500)
  }
})

// DELETE /api/roles/[id] - Delete role
export const DELETE = withPermission('SETTINGS', 'UPDATE', async (
  request: NextRequest,
  user: any,
  context?: { params: { id: string } }
) => {
  const id = context?.params?.id

  if (!id) {
    return createErrorResponse('Role ID is required', 400)
  }

  // Prevent deletion of predefined roles
  const predefinedRoleIds = ['founder', 'super_admin', 'admin', 'staff', 'distributor']
  if (predefinedRoleIds.includes(id)) {
    return createErrorResponse('Cannot delete predefined roles', 400)
  }

  // Delete the role
  const deleted = roleStorage.delete(id)

  if (!deleted) {
    return createErrorResponse('Role not found', 404)
  }

  console.log('Deleted custom role:', id)
  console.log('Total custom roles remaining:', roleStorage.getAll().length)

  return createSuccessResponse({ id }, 'Role deleted successfully')
})
