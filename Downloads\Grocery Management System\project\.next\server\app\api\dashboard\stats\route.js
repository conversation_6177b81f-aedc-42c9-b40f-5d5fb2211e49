"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.getAuthUser)(request);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const storeId = user.storeId;\n        if (!storeId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Store not found\"\n            }, {\n                status: 400\n            });\n        }\n        // Get today's date range\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        // Get this month's date range\n        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\n        // Fetch statistics\n        const [totalSales, totalPurchases, totalCustomers, totalProducts, lowStockItems, todaySales, thisMonthSales, pendingOrders] = await Promise.all([\n            // Total sales count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.sale.count({\n                where: {\n                    storeId,\n                    status: \"COMPLETED\"\n                }\n            }),\n            // Total purchases count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.purchase.count({\n                where: {\n                    storeId\n                }\n            }),\n            // Total customers count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.customer.count({\n                where: {\n                    storeId,\n                    isActive: true\n                }\n            }),\n            // Total products count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.count({\n                where: {\n                    storeId,\n                    isActive: true\n                }\n            }),\n            // Low stock items count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.inventory.count({\n                where: {\n                    storeId,\n                    OR: [\n                        {\n                            quantity: {\n                                lte: _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.inventory.fields.reorderLevel\n                            }\n                        }\n                    ]\n                }\n            }),\n            // Today's sales amount\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.sale.aggregate({\n                where: {\n                    storeId,\n                    status: \"COMPLETED\",\n                    createdAt: {\n                        gte: today,\n                        lt: tomorrow\n                    }\n                },\n                _sum: {\n                    totalAmount: true\n                }\n            }),\n            // This month's sales amount\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.sale.aggregate({\n                where: {\n                    storeId,\n                    status: \"COMPLETED\",\n                    createdAt: {\n                        gte: thisMonth,\n                        lt: nextMonth\n                    }\n                },\n                _sum: {\n                    totalAmount: true\n                }\n            }),\n            // Pending orders count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.purchase.count({\n                where: {\n                    storeId,\n                    status: \"PENDING\"\n                }\n            })\n        ]);\n        const stats = {\n            totalSales,\n            totalPurchases,\n            totalCustomers,\n            totalProducts,\n            lowStockItems,\n            todaySales: todaySales._sum.totalAmount || 0,\n            thisMonthSales: thisMonthSales._sum.totalAmount || 0,\n            pendingOrders\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(stats);\n    } catch (error) {\n        console.error(\"Dashboard stats error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n// Higher-order function for API route protection\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const authResult = await checkPermission(request, module, permission);\n            if (authResult.error) {\n                const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n                return createErrorResponse(authResult.error, authResult.status || 401);\n            }\n            return await handler(request, authResult.user);\n        } catch (error) {\n            console.error(\"Permission middleware error:\", error);\n            const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();