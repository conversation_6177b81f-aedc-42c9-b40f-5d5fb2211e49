import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { NextRequest } from 'next/server'
import { prisma } from './prisma'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export interface JWTPayload {
  userId: string
  email: string
  role: string
  storeId?: string
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export function signToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })
}

export function verifyToken(token: string): JWTPayload {
  return jwt.verify(token, JWT_SECRET) as JWTPayload
}

export async function getAuthUser(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return null
    }

    const payload = verifyToken(token)

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: { store: true }
    })

    if (!user || !user.isActive) {
      return null
    }

    return user
  } catch (error) {
    console.error('Auth error:', error)
    return null
  }
}

export function createAuthResponse(user: any, token: string) {
  return {
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      storeId: user.storeId,
      store: user.store
    },
    token
  }
}

// Permission-based middleware helper
export async function checkPermission(
  request: NextRequest,
  module: string,
  permission: string
) {
  const user = await getAuthUser(request)

  if (!user) {
    return { error: 'Unauthorized', status: 401 }
  }

  // Import permissions dynamically to avoid circular dependency
  const { hasPermission } = await import('./permissions')

  if (!hasPermission(user.role as any, module as any, permission as any)) {
    return { error: 'Insufficient permissions', status: 403 }
  }

  return { user, error: null }
}

// Higher-order function for API route protection
export function withPermission(
  module: string,
  permission: string,
  handler: (request: NextRequest, user: any) => Promise<Response>
) {
  return async (request: NextRequest) => {
    try {
      const authResult = await checkPermission(request, module, permission)

      if (authResult.error) {
        const { createErrorResponse } = await import('./api-utils')
        return createErrorResponse(authResult.error, authResult.status || 401)
      }

      return await handler(request, authResult.user)
    } catch (error) {
      console.error('Permission middleware error:', error)
      const { createErrorResponse } = await import('./api-utils')
      return createErrorResponse('Internal server error', 500)
    }
  }
}