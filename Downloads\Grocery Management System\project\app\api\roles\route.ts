import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { roleStorage, predefinedRoles, CustomRole } from '@/lib/role-storage'

// Schema for custom role creation
const customRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  description: z.string().optional(),
  modules: z.array(z.string()),
  permissions: z.record(z.array(z.string()))
})

// GET /api/roles - Get all roles
export async function GET(request: NextRequest) {
  try {
    const customRoles = roleStorage.getAll()
    const allRoles = [...predefinedRoles, ...customRoles]

    return NextResponse.json({
      success: true,
      data: allRoles,
      message: 'Roles fetched successfully'
    })
  } catch (error) {
    console.error('Error fetching roles:', error)
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    )
  }
}

// POST /api/roles - Create custom role
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const data = customRoleSchema.parse(body)

    // Create custom role object
    const customRole: CustomRole = {
      id: `custom_${Date.now()}`,
      name: data.name.toUpperCase().replace(/\s+/g, '_'),
      description: data.description || '',
      isCustom: true,
      modules: data.modules,
      permissions: data.permissions,
      moduleCount: data.modules.length,
      permissionCount: Object.values(data.permissions).reduce((acc: number, perms: any) => acc + perms.length, 0),
      createdAt: new Date().toISOString()
    }

    // Add to storage
    roleStorage.add(customRole)

    console.log('Creating custom role:', customRole)
    console.log('Total custom roles:', roleStorage.getAll().length)

    return NextResponse.json({
      success: true,
      data: customRole,
      message: 'Custom role created successfully'
    })
  } catch (error) {
    console.error('Error creating custom role:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid role data', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create custom role' },
      { status: 500 }
    )
  }
}

// PUT /api/roles - Update role (legacy endpoint, use /api/roles/[id] instead)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Role ID is required' },
        { status: 400 }
      )
    }

    // Update custom role
    const updatedRole = roleStorage.update(id, updateData)

    if (!updatedRole) {
      return NextResponse.json(
        { error: 'Role not found or cannot be updated' },
        { status: 404 }
      )
    }

    console.log('Updated role:', updatedRole)

    return NextResponse.json({
      success: true,
      data: updatedRole,
      message: 'Role updated successfully'
    })
  } catch (error) {
    console.error('Error updating role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
}

// DELETE /api/roles - Delete custom role
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Role ID is required' },
        { status: 400 }
      )
    }

    // Prevent deletion of predefined roles
    const predefinedRoles = ['founder', 'super_admin', 'admin', 'staff', 'distributor']
    if (predefinedRoles.includes(id)) {
      return NextResponse.json(
        { error: 'Cannot delete predefined roles' },
        { status: 400 }
      )
    }

    // Remove from custom roles
    const deleted = roleStorage.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      )
    }

    console.log('Deleted custom role:', id)
    console.log('Total custom roles remaining:', roleStorage.getAll().length)

    return NextResponse.json({
      success: true,
      data: { id },
      message: 'Custom role deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting role:', error)
    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    )
  }
}
