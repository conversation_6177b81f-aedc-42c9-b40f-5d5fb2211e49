import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Schema for custom role creation
const customRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  description: z.string().optional(),
  modules: z.array(z.string()),
  permissions: z.record(z.array(z.string()))
})

// Temporary storage for custom roles (in production, use database)
let customRoles: any[] = []

// GET /api/roles - Get all roles
export async function GET(request: NextRequest) {
  try {
    // Predefined roles
    const predefinedRoles = [
      {
        id: 'founder',
        name: 'FOUNDE<PERSON>',
        description: 'System founder with complete access',
        isCustom: false,
        moduleCount: 27,
        permissionCount: 150,
        createdAt: new Date().toISOString()
      },
      {
        id: 'super_admin',
        name: 'SUPER_ADMIN',
        description: 'Super administrator with extensive access',
        isCustom: false,
        moduleCount: 25,
        permissionCount: 120,
        createdAt: new Date().toISOString()
      },
      {
        id: 'admin',
        name: 'ADMI<PERSON>',
        description: 'Administrator with operational access',
        isCustom: false,
        moduleCount: 20,
        permissionCount: 80,
        createdAt: new Date().toISOString()
      },
      {
        id: 'staff',
        name: 'STAFF',
        description: 'Staff member with limited access',
        isCustom: false,
        moduleCount: 10,
        permissionCount: 30,
        createdAt: new Date().toISOString()
      },
      {
        id: 'distributor',
        name: 'DISTRIBUTOR',
        description: 'Distributor with specific access',
        isCustom: false,
        moduleCount: 8,
        permissionCount: 25,
        createdAt: new Date().toISOString()
      }
    ]

    const allRoles = [...predefinedRoles, ...customRoles]

    return NextResponse.json({
      success: true,
      data: allRoles,
      message: 'Roles fetched successfully'
    })
  } catch (error) {
    console.error('Error fetching roles:', error)
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    )
  }
}

// POST /api/roles - Create custom role
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const data = customRoleSchema.parse(body)

    // Create custom role object
    const customRole = {
      id: `custom_${Date.now()}`,
      name: data.name.toUpperCase().replace(/\s+/g, '_'),
      description: data.description || '',
      isCustom: true,
      modules: data.modules,
      permissions: data.permissions,
      moduleCount: data.modules.length,
      permissionCount: Object.values(data.permissions).reduce((acc: number, perms: any) => acc + perms.length, 0),
      createdAt: new Date().toISOString()
    }

    // Add to temporary storage
    customRoles.push(customRole)

    console.log('Creating custom role:', customRole)
    console.log('Total custom roles:', customRoles.length)

    return NextResponse.json({
      success: true,
      data: customRole,
      message: 'Custom role created successfully'
    })
  } catch (error) {
    console.error('Error creating custom role:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid role data', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create custom role' },
      { status: 500 }
    )
  }
}

// PUT /api/roles/[id] - Update role
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    // Find and update custom role
    const roleIndex = customRoles.findIndex(role => role.id === id)
    if (roleIndex !== -1) {
      customRoles[roleIndex] = { ...customRoles[roleIndex], ...updateData }
      console.log('Updated role:', customRoles[roleIndex])
    }

    return NextResponse.json({
      success: true,
      data: { id, ...updateData },
      message: 'Role updated successfully'
    })
  } catch (error) {
    console.error('Error updating role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
}

// DELETE /api/roles - Delete custom role
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Role ID is required' },
        { status: 400 }
      )
    }

    // Prevent deletion of predefined roles
    const predefinedRoles = ['founder', 'super_admin', 'admin', 'staff', 'distributor']
    if (predefinedRoles.includes(id)) {
      return NextResponse.json(
        { error: 'Cannot delete predefined roles' },
        { status: 400 }
      )
    }

    // Remove from custom roles
    const initialLength = customRoles.length
    customRoles = customRoles.filter(role => role.id !== id)

    console.log('Deleting custom role:', id)
    console.log('Roles before:', initialLength, 'after:', customRoles.length)

    return NextResponse.json({
      success: true,
      data: { id },
      message: 'Custom role deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting role:', error)
    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    )
  }
}
