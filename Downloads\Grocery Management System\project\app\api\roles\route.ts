import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { roleStorage, predefinedRoles, CustomRole } from '@/lib/role-storage'
import { withPermission, createSuccessResponse, createErrorResponse } from '@/lib/api-utils'

// Schema for custom role creation
const customRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  description: z.string().optional(),
  modules: z.array(z.string()),
  permissions: z.record(z.array(z.string()))
})

// GET /api/roles - Get all roles
export async function GET(request: NextRequest) {
  console.log('🔥 GET /api/roles ROUTE CALLED - BEFORE withPermission')

  return withPermission('SETTINGS', 'READ', async (request: NextRequest, user: any) => {
    console.log('🔥 GET /api/roles called by user:', user?.id || 'unknown')

    const customRoles = roleStorage.getAll()
    const allRoles = [...predefinedRoles, ...customRoles]

    console.log('🔥 Returning roles:', { total: allRoles.length, custom: customRoles.length })
    return createSuccessResponse(allRoles, 'Roles fetched successfully')
  })(request)
}

// POST /api/roles - Create custom role
export const POST = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  console.log('POST /api/roles called by user:', user?.id || 'unknown')

  try {
    const body = await request.json()
    console.log('Request body:', body)

    const data = customRoleSchema.parse(body)
    console.log('Parsed data:', data)

    // Create custom role object
    const customRole: CustomRole = {
      id: `custom_${Date.now()}`,
      name: data.name.toUpperCase().replace(/\s+/g, '_'),
      description: data.description || '',
      isCustom: true,
      modules: data.modules,
      permissions: data.permissions,
      moduleCount: data.modules.length,
      permissionCount: Object.values(data.permissions).reduce((acc: number, perms: any) => acc + perms.length, 0),
      createdAt: new Date().toISOString()
    }

    // Add to storage
    roleStorage.add(customRole)

    console.log('Creating custom role:', customRole)
    console.log('Total custom roles:', roleStorage.getAll().length)

    return createSuccessResponse(customRole, 'Custom role created successfully')
  } catch (error) {
    console.error('Error creating custom role:', error)
    if (error instanceof z.ZodError) {
      console.error('Validation errors:', error.errors)
      return createErrorResponse(`Invalid role data: ${error.errors.map(e => e.message).join(', ')}`, 400)
    }
    return createErrorResponse('Failed to create custom role', 500)
  }
})

// PUT /api/roles - Update role (legacy endpoint, use /api/roles/[id] instead)
export const PUT = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  const body = await request.json()
  const { id, ...updateData } = body

  if (!id) {
    return createErrorResponse('Role ID is required', 400)
  }

  // Update custom role
  const updatedRole = roleStorage.update(id, updateData)

  if (!updatedRole) {
    return createErrorResponse('Role not found or cannot be updated', 404)
  }

  console.log('Updated role:', updatedRole)

  return createSuccessResponse(updatedRole, 'Role updated successfully')
})

// DELETE /api/roles - Delete custom role
export const DELETE = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  const url = new URL(request.url)
  const id = url.searchParams.get('id')

  if (!id) {
    return createErrorResponse('Role ID is required', 400)
  }

  // Prevent deletion of predefined roles
  const predefinedRoleIds = ['founder', 'super_admin', 'admin', 'staff', 'distributor']
  if (predefinedRoleIds.includes(id)) {
    return createErrorResponse('Cannot delete predefined roles', 400)
  }

  // Remove from custom roles
  const deleted = roleStorage.delete(id)

  if (!deleted) {
    return createErrorResponse('Role not found', 404)
  }

  console.log('Deleted custom role:', id)
  console.log('Total custom roles remaining:', roleStorage.getAll().length)

  return createSuccessResponse({ id }, 'Custom role deleted successfully')
})
