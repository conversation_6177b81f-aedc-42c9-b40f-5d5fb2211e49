// Simple test script to verify role API functionality
const testRoleAPI = async () => {
  const baseURL = 'http://localhost:3000/api';
  
  console.log('Testing Role API...\n');
  
  try {
    // 1. Test GET /api/roles - Get all roles
    console.log('1. Testing GET /api/roles');
    const getRolesResponse = await fetch(`${baseURL}/roles`);
    const getRolesData = await getRolesResponse.json();
    console.log('Response:', getRolesData);
    console.log('Total roles:', getRolesData.data?.length || 0);
    console.log('');
    
    // 2. Test POST /api/roles - Create custom role
    console.log('2. Testing POST /api/roles - Create custom role');
    const customRoleData = {
      name: 'MANAGER',
      description: 'Store manager with operational access',
      modules: ['PRODUCT', 'INVENTORY', 'SALES', 'CUSTOMER'],
      permissions: {
        'PRODUCT': ['CREATE', 'READ', 'UPDATE'],
        'INVENTORY': ['READ', 'UPDATE'],
        'SALES': ['CREATE', 'READ'],
        'CUSTOMER': ['CREATE', 'READ', 'UPDATE']
      }
    };
    
    const createRoleResponse = await fetch(`${baseURL}/roles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(customRoleData)
    });
    
    const createRoleData = await createRoleResponse.json();
    console.log('Create response:', createRoleData);
    
    if (createRoleData.success) {
      const newRoleId = createRoleData.data.id;
      console.log('Created role ID:', newRoleId);
      
      // 3. Test GET /api/roles again to see the new role
      console.log('\n3. Testing GET /api/roles after creation');
      const getRolesResponse2 = await fetch(`${baseURL}/roles`);
      const getRolesData2 = await getRolesResponse2.json();
      console.log('Total roles after creation:', getRolesData2.data?.length || 0);
      console.log('Custom roles:', getRolesData2.data?.filter(r => r.isCustom) || []);
      
      // 4. Test GET /api/roles/[id] - Get specific role
      console.log('\n4. Testing GET /api/roles/[id]');
      const getRoleResponse = await fetch(`${baseURL}/roles/${newRoleId}`);
      const getRoleData = await getRoleResponse.json();
      console.log('Get specific role response:', getRoleData);
      
      // 5. Test PUT /api/roles/[id] - Update role
      console.log('\n5. Testing PUT /api/roles/[id] - Update role');
      const updateData = {
        description: 'Updated store manager description',
        modules: ['PRODUCT', 'INVENTORY', 'SALES', 'CUSTOMER', 'REPORTS']
      };
      
      const updateRoleResponse = await fetch(`${baseURL}/roles/${newRoleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });
      
      const updateRoleData = await updateRoleResponse.json();
      console.log('Update response:', updateRoleData);
      
      // 6. Test DELETE /api/roles/[id] - Delete role
      console.log('\n6. Testing DELETE /api/roles/[id]');
      const deleteRoleResponse = await fetch(`${baseURL}/roles/${newRoleId}`, {
        method: 'DELETE'
      });
      
      const deleteRoleData = await deleteRoleResponse.json();
      console.log('Delete response:', deleteRoleData);
      
      // 7. Final check - GET all roles to confirm deletion
      console.log('\n7. Final check - GET all roles after deletion');
      const getRolesResponse3 = await fetch(`${baseURL}/roles`);
      const getRolesData3 = await getRolesResponse3.json();
      console.log('Total roles after deletion:', getRolesData3.data?.length || 0);
      console.log('Custom roles remaining:', getRolesData3.data?.filter(r => r.isCustom) || []);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testRoleAPI();
} else {
  // Browser environment
  console.log('Run testRoleAPI() in the browser console to test the API');
  window.testRoleAPI = testRoleAPI;
}
