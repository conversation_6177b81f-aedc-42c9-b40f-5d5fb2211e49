"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/roles/route";
exports.ids = ["app/api/roles/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Froute&page=%2Fapi%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Froute&page=%2Fapi%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/roles/route.ts */ \"(rsc)/./app/api/roles/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/roles/route\",\n        pathname: \"/api/roles\",\n        filename: \"route\",\n        bundlePath: \"app/api/roles/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\api\\\\roles\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/roles/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Froute&page=%2Fapi%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/roles/route.ts":
/*!********************************!*\
  !*** ./app/api/roles/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/role-storage */ \"(rsc)/./lib/role-storage.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n\n\n\n// Schema for custom role creation\nconst customRoleSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Role name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    modules: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    permissions: zod__WEBPACK_IMPORTED_MODULE_0__.z.record(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()))\n});\n// GET /api/roles - Get all roles\nasync function GET(request) {\n    console.log(\"\\uD83D\\uDD25 GET /api/roles ROUTE CALLED - BEFORE withPermission\");\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"READ\", async (request, user)=>{\n        console.log(\"\\uD83D\\uDD25 GET /api/roles called by user:\", user?.id || \"unknown\");\n        const customRoles = _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.roleStorage.getAll();\n        const allRoles = [\n            ..._lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.predefinedRoles,\n            ...customRoles\n        ];\n        console.log(\"\\uD83D\\uDD25 Returning roles:\", {\n            total: allRoles.length,\n            custom: customRoles.length\n        });\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)(allRoles, \"Roles fetched successfully\");\n    })(request);\n}\n// POST /api/roles - Create custom role\nconst POST = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"UPDATE\", async (request, user)=>{\n    console.log(\"POST /api/roles called by user:\", user?.id || \"unknown\");\n    try {\n        const body = await request.json();\n        console.log(\"Request body:\", body);\n        const data = customRoleSchema.parse(body);\n        console.log(\"Parsed data:\", data);\n        // Create custom role object\n        const customRole = {\n            id: `custom_${Date.now()}`,\n            name: data.name.toUpperCase().replace(/\\s+/g, \"_\"),\n            description: data.description || \"\",\n            isCustom: true,\n            modules: data.modules,\n            permissions: data.permissions,\n            moduleCount: data.modules.length,\n            permissionCount: Object.values(data.permissions).reduce((acc, perms)=>acc + perms.length, 0),\n            createdAt: new Date().toISOString()\n        };\n        // Add to storage\n        _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.roleStorage.add(customRole);\n        console.log(\"Creating custom role:\", customRole);\n        console.log(\"Total custom roles:\", _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.roleStorage.getAll().length);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)(customRole, \"Custom role created successfully\");\n    } catch (error) {\n        console.error(\"Error creating custom role:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_0__.z.ZodError) {\n            console.error(\"Validation errors:\", error.errors);\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(`Invalid role data: ${error.errors.map((e)=>e.message).join(\", \")}`, 400);\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Failed to create custom role\", 500);\n    }\n});\n// PUT /api/roles - Update role (legacy endpoint, use /api/roles/[id] instead)\nconst PUT = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"UPDATE\", async (request, user)=>{\n    const body = await request.json();\n    const { id, ...updateData } = body;\n    if (!id) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Role ID is required\", 400);\n    }\n    // Update custom role\n    const updatedRole = _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.roleStorage.update(id, updateData);\n    if (!updatedRole) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Role not found or cannot be updated\", 404);\n    }\n    console.log(\"Updated role:\", updatedRole);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)(updatedRole, \"Role updated successfully\");\n});\n// DELETE /api/roles - Delete custom role\nconst DELETE = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"UPDATE\", async (request, user)=>{\n    const url = new URL(request.url);\n    const id = url.searchParams.get(\"id\");\n    if (!id) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Role ID is required\", 400);\n    }\n    // Prevent deletion of predefined roles\n    const predefinedRoleIds = [\n        \"founder\",\n        \"super_admin\",\n        \"admin\",\n        \"staff\",\n        \"distributor\"\n    ];\n    if (predefinedRoleIds.includes(id)) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Cannot delete predefined roles\", 400);\n    }\n    // Remove from custom roles\n    const deleted = _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.roleStorage.delete(id);\n    if (!deleted) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Role not found\", 404);\n    }\n    console.log(\"Deleted custom role:\", id);\n    console.log(\"Total custom roles remaining:\", _lib_role_storage__WEBPACK_IMPORTED_MODULE_1__.roleStorage.getAll().length);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        id\n    }, \"Custom role deleted successfully\");\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/roles/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request, context)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user, context);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXBpLXV0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ2Y7QUFDUDtBQUVqQyxnQ0FBZ0M7QUFDekIsU0FBU0csZUFBZUMsSUFBUyxFQUFFQyxTQUFpQixHQUFHO0lBQzVELE9BQU9MLGtGQUFZQSxDQUFDTSxJQUFJLENBQUNGLE1BQU07UUFBRUM7SUFBTztBQUMxQztBQUVBLHlCQUF5QjtBQUNsQixTQUFTRSxvQkFBb0JDLE9BQWUsRUFBRUgsU0FBaUIsR0FBRztJQUN2RSxPQUFPTCxrRkFBWUEsQ0FBQ00sSUFBSSxDQUFDO1FBQUVHLE9BQU9EO0lBQVEsR0FBRztRQUFFSDtJQUFPO0FBQ3hEO0FBRUEsMkJBQTJCO0FBQ3BCLFNBQVNLLHNCQUFzQk4sSUFBUyxFQUFFSSxPQUFnQjtJQUMvRCxPQUFPUixrRkFBWUEsQ0FBQ00sSUFBSSxDQUFDO1FBQ3ZCSyxTQUFTO1FBQ1RQO1FBQ0FJO0lBQ0Y7QUFDRjtBQUVBLDZCQUE2QjtBQUN0QixTQUFTSSx3QkFDZFIsSUFBVyxFQUNYUyxJQUFZLEVBQ1pDLEtBQWEsRUFDYkMsS0FBYTtJQUViLE9BQU9mLGtGQUFZQSxDQUFDTSxJQUFJLENBQUM7UUFDdkJGO1FBQ0FZLFlBQVk7WUFDVkg7WUFDQUM7WUFDQUM7WUFDQUUsWUFBWUMsS0FBS0MsSUFBSSxDQUFDSixRQUFRRDtZQUM5Qk0sU0FBU1AsT0FBT0MsUUFBUUM7WUFDeEJNLFNBQVNSLE9BQU87UUFDbEI7SUFDRjtBQUNGO0FBRUEsdUNBQXVDO0FBQ2hDLFNBQVNTLGVBQ2RDLE1BQWMsRUFDZEMsVUFBa0IsRUFDbEJDLE9BQWtGO0lBRWxGLE9BQU8sT0FBT0MsU0FBc0JDO1FBQ2xDLElBQUk7WUFDRixNQUFNQyxrQkFBa0IsTUFBTTNCLHNEQUFlQSxDQUFDeUIsU0FBU0gsUUFBUUM7WUFFL0QsSUFBSUksZ0JBQWdCbkIsS0FBSyxFQUFFO2dCQUN6QixPQUFPRixvQkFBb0JxQixnQkFBZ0JuQixLQUFLLEVBQUVtQixnQkFBZ0J2QixNQUFNO1lBQzFFO1lBRUEsT0FBTyxNQUFNb0IsUUFBUUMsU0FBU0UsZ0JBQWdCQyxJQUFJLEVBQUVGO1FBQ3RELEVBQUUsT0FBT2xCLE9BQU87WUFDZHFCLFFBQVFyQixLQUFLLENBQUMsQ0FBQyxhQUFhLEVBQUVjLE9BQU8sQ0FBQyxDQUFDLEVBQUVkO1lBQ3pDLE9BQU9GLG9CQUFvQix5QkFBeUI7UUFDdEQ7SUFDRjtBQUNGO0FBRUEsbUJBQW1CO0FBQ1osZUFBZXdCLGVBQ3BCQyxNQUFjLEVBQ2RULE1BQWMsRUFDZFUsT0FBZSxFQUNmQyxNQUFjLEVBQ2RDLE9BQWU7SUFFZixJQUFJO1FBQ0YsTUFBTWpDLDJDQUFNQSxDQUFDa0MsUUFBUSxDQUFDQyxNQUFNLENBQUM7WUFDM0JqQyxNQUFNO2dCQUNKNEI7Z0JBQ0FUO2dCQUNBVTtnQkFDQUM7Z0JBQ0FDO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBTzFCLE9BQU87UUFDZHFCLFFBQVFyQixLQUFLLENBQUMsK0JBQStCQTtJQUMvQztBQUNGO0FBRUEsb0JBQW9CO0FBQ2IsU0FBUzZCLG9CQUFvQlosT0FBb0I7SUFDdEQsTUFBTWEsTUFBTSxJQUFJQyxJQUFJZCxRQUFRYSxHQUFHO0lBQy9CLE1BQU0xQixPQUFPNEIsU0FBU0YsSUFBSUcsWUFBWSxDQUFDQyxHQUFHLENBQUMsV0FBVztJQUN0RCxNQUFNN0IsUUFBUTJCLFNBQVNGLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDLFlBQVk7SUFDeEQsTUFBTUMsU0FBU0wsSUFBSUcsWUFBWSxDQUFDQyxHQUFHLENBQUMsYUFBYTtJQUNqRCxNQUFNRSxTQUFTTixJQUFJRyxZQUFZLENBQUNDLEdBQUcsQ0FBQyxhQUFhO0lBQ2pELE1BQU1HLFlBQVlQLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDLGdCQUFnQjtJQUV2RCxPQUFPO1FBQ0w5QixNQUFNSyxLQUFLNkIsR0FBRyxDQUFDLEdBQUdsQztRQUNsQkMsT0FBT0ksS0FBSzhCLEdBQUcsQ0FBQyxLQUFLOUIsS0FBSzZCLEdBQUcsQ0FBQyxHQUFHakM7UUFDakNtQyxNQUFNLENBQUMvQixLQUFLNkIsR0FBRyxDQUFDLEdBQUdsQyxRQUFRLEtBQUtLLEtBQUs4QixHQUFHLENBQUMsS0FBSzlCLEtBQUs2QixHQUFHLENBQUMsR0FBR2pDO1FBQzFEOEI7UUFDQUM7UUFDQUMsV0FBV0EsY0FBYyxRQUFRLFFBQVE7SUFDM0M7QUFDRjtBQUVBLHVCQUF1QjtBQUNoQixTQUFTSSxrQkFBa0JOLE1BQWMsRUFBRU8sTUFBZ0I7SUFDaEUsSUFBSSxDQUFDUCxRQUFRLE9BQU8sQ0FBQztJQUVyQixPQUFPO1FBQ0xRLElBQUlELE9BQU9FLEdBQUcsQ0FBQ0MsQ0FBQUEsUUFBVTtnQkFDdkIsQ0FBQ0EsTUFBTSxFQUFFO29CQUNQQyxVQUFVWDtvQkFDVlksTUFBTTtnQkFDUjtZQUNGO0lBQ0Y7QUFDRjtBQUVBLDJCQUEyQjtBQUNwQixTQUFTQyxxQkFDZEMsU0FBa0IsRUFDbEJDLE9BQWdCLEVBQ2hCTCxRQUFnQixXQUFXO0lBRTNCLE1BQU1NLFNBQWMsQ0FBQztJQUVyQixJQUFJRixXQUFXO1FBQ2JFLE1BQU0sQ0FBQ04sTUFBTSxHQUFHO1lBQUUsR0FBR00sTUFBTSxDQUFDTixNQUFNO1lBQUVPLEtBQUssSUFBSUMsS0FBS0o7UUFBVztJQUMvRDtJQUVBLElBQUlDLFNBQVM7UUFDWCxNQUFNSSxNQUFNLElBQUlELEtBQUtIO1FBQ3JCSSxJQUFJQyxRQUFRLENBQUMsSUFBSSxJQUFJLElBQUk7UUFDekJKLE1BQU0sQ0FBQ04sTUFBTSxHQUFHO1lBQUUsR0FBR00sTUFBTSxDQUFDTixNQUFNO1lBQUVXLEtBQUtGO1FBQUk7SUFDL0M7SUFFQSxPQUFPRyxPQUFPQyxJQUFJLENBQUNQLFFBQVFRLE1BQU0sR0FBRyxJQUFJUixTQUFTLENBQUM7QUFDcEQ7QUFFQSxvQkFBb0I7QUFDYixTQUFTUyx1QkFBdUJqRSxJQUFTLEVBQUUrQyxNQUFnQjtJQUNoRSxNQUFNbUIsVUFBVW5CLE9BQU9TLE1BQU0sQ0FBQ04sQ0FBQUEsUUFBUyxDQUFDbEQsSUFBSSxDQUFDa0QsTUFBTTtJQUVuRCxJQUFJZ0IsUUFBUUYsTUFBTSxHQUFHLEdBQUc7UUFDdEIsTUFBTSxJQUFJRyxNQUFNLENBQUMseUJBQXlCLEVBQUVELFFBQVFFLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDbEU7QUFDRjtBQUVBLDBCQUEwQjtBQUNuQixTQUFTQyxvQkFBb0I1QyxJQUFTO0lBQzNDLElBQUksQ0FBQ0EsS0FBS00sT0FBTyxFQUFFO1FBQ2pCLE1BQU0sSUFBSW9DLE1BQU07SUFDbEI7SUFDQSxPQUFPMUMsS0FBS00sT0FBTztBQUNyQiIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvYXBpLXV0aWxzLnRzPzdkYTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgY2hlY2tQZXJtaXNzaW9uIH0gZnJvbSAnLi9hdXRoJ1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnXG5cbi8vIFN0YW5kYXJkIEFQSSByZXNwb25zZSB3cmFwcGVyXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlUmVzcG9uc2UoZGF0YTogYW55LCBzdGF0dXM6IG51bWJlciA9IDIwMCkge1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZGF0YSwgeyBzdGF0dXMgfSlcbn1cblxuLy8gRXJyb3IgcmVzcG9uc2Ugd3JhcHBlclxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUVycm9yUmVzcG9uc2UobWVzc2FnZTogc3RyaW5nLCBzdGF0dXM6IG51bWJlciA9IDQwMCkge1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogbWVzc2FnZSB9LCB7IHN0YXR1cyB9KVxufVxuXG4vLyBTdWNjZXNzIHJlc3BvbnNlIHdyYXBwZXJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTdWNjZXNzUmVzcG9uc2UoZGF0YTogYW55LCBtZXNzYWdlPzogc3RyaW5nKSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICBkYXRhLFxuICAgIG1lc3NhZ2VcbiAgfSlcbn1cblxuLy8gUGFnaW5hdGVkIHJlc3BvbnNlIHdyYXBwZXJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVQYWdpbmF0ZWRSZXNwb25zZShcbiAgZGF0YTogYW55W10sXG4gIHBhZ2U6IG51bWJlcixcbiAgbGltaXQ6IG51bWJlcixcbiAgdG90YWw6IG51bWJlclxuKSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgZGF0YSxcbiAgICBwYWdpbmF0aW9uOiB7XG4gICAgICBwYWdlLFxuICAgICAgbGltaXQsXG4gICAgICB0b3RhbCxcbiAgICAgIHRvdGFsUGFnZXM6IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KSxcbiAgICAgIGhhc05leHQ6IHBhZ2UgKiBsaW1pdCA8IHRvdGFsLFxuICAgICAgaGFzUHJldjogcGFnZSA+IDFcbiAgICB9XG4gIH0pXG59XG5cbi8vIFBlcm1pc3Npb24tYmFzZWQgQVBJIGhhbmRsZXIgd3JhcHBlclxuZXhwb3J0IGZ1bmN0aW9uIHdpdGhQZXJtaXNzaW9uKFxuICBtb2R1bGU6IHN0cmluZyxcbiAgcGVybWlzc2lvbjogc3RyaW5nLFxuICBoYW5kbGVyOiAocmVxdWVzdDogTmV4dFJlcXVlc3QsIHVzZXI6IGFueSwgY29udGV4dD86IGFueSkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+XG4pIHtcbiAgcmV0dXJuIGFzeW5jIChyZXF1ZXN0OiBOZXh0UmVxdWVzdCwgY29udGV4dD86IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwZXJtaXNzaW9uQ2hlY2sgPSBhd2FpdCBjaGVja1Blcm1pc3Npb24ocmVxdWVzdCwgbW9kdWxlLCBwZXJtaXNzaW9uKVxuXG4gICAgICBpZiAocGVybWlzc2lvbkNoZWNrLmVycm9yKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVFcnJvclJlc3BvbnNlKHBlcm1pc3Npb25DaGVjay5lcnJvciwgcGVybWlzc2lvbkNoZWNrLnN0YXR1cylcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGF3YWl0IGhhbmRsZXIocmVxdWVzdCwgcGVybWlzc2lvbkNoZWNrLnVzZXIsIGNvbnRleHQpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEFQSSBFcnJvciBpbiAke21vZHVsZX06YCwgZXJyb3IpXG4gICAgICByZXR1cm4gY3JlYXRlRXJyb3JSZXNwb25zZSgnSW50ZXJuYWwgc2VydmVyIGVycm9yJywgNTAwKVxuICAgIH1cbiAgfVxufVxuXG4vLyBBdWRpdCBsb2cgaGVscGVyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQXVkaXRMb2coXG4gIGFjdGlvbjogc3RyaW5nLFxuICBtb2R1bGU6IHN0cmluZyxcbiAgZGV0YWlsczogc3RyaW5nLFxuICB1c2VySWQ6IHN0cmluZyxcbiAgc3RvcmVJZDogc3RyaW5nXG4pIHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBwcmlzbWEuYXVkaXRMb2cuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgYWN0aW9uLFxuICAgICAgICBtb2R1bGUsXG4gICAgICAgIGRldGFpbHMsXG4gICAgICAgIHVzZXJJZCxcbiAgICAgICAgc3RvcmVJZFxuICAgICAgfVxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBhdWRpdCBsb2c6JywgZXJyb3IpXG4gIH1cbn1cblxuLy8gUGFnaW5hdGlvbiBoZWxwZXJcbmV4cG9ydCBmdW5jdGlvbiBnZXRQYWdpbmF0aW9uUGFyYW1zKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIGNvbnN0IHVybCA9IG5ldyBVUkwocmVxdWVzdC51cmwpXG4gIGNvbnN0IHBhZ2UgPSBwYXJzZUludCh1cmwuc2VhcmNoUGFyYW1zLmdldCgncGFnZScpIHx8ICcxJylcbiAgY29uc3QgbGltaXQgPSBwYXJzZUludCh1cmwuc2VhcmNoUGFyYW1zLmdldCgnbGltaXQnKSB8fCAnMTAnKVxuICBjb25zdCBzZWFyY2ggPSB1cmwuc2VhcmNoUGFyYW1zLmdldCgnc2VhcmNoJykgfHwgJydcbiAgY29uc3Qgc29ydEJ5ID0gdXJsLnNlYXJjaFBhcmFtcy5nZXQoJ3NvcnRCeScpIHx8ICdjcmVhdGVkQXQnXG4gIGNvbnN0IHNvcnRPcmRlciA9IHVybC5zZWFyY2hQYXJhbXMuZ2V0KCdzb3J0T3JkZXInKSB8fCAnZGVzYydcblxuICByZXR1cm4ge1xuICAgIHBhZ2U6IE1hdGgubWF4KDEsIHBhZ2UpLFxuICAgIGxpbWl0OiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDEsIGxpbWl0KSksXG4gICAgc2tpcDogKE1hdGgubWF4KDEsIHBhZ2UpIC0gMSkgKiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDEsIGxpbWl0KSksXG4gICAgc2VhcmNoLFxuICAgIHNvcnRCeSxcbiAgICBzb3J0T3JkZXI6IHNvcnRPcmRlciA9PT0gJ2FzYycgPyAnYXNjJyA6ICdkZXNjJ1xuICB9XG59XG5cbi8vIFNlYXJjaCBmaWx0ZXIgaGVscGVyXG5leHBvcnQgZnVuY3Rpb24gYnVpbGRTZWFyY2hGaWx0ZXIoc2VhcmNoOiBzdHJpbmcsIGZpZWxkczogc3RyaW5nW10pIHtcbiAgaWYgKCFzZWFyY2gpIHJldHVybiB7fVxuXG4gIHJldHVybiB7XG4gICAgT1I6IGZpZWxkcy5tYXAoZmllbGQgPT4gKHtcbiAgICAgIFtmaWVsZF06IHtcbiAgICAgICAgY29udGFpbnM6IHNlYXJjaCxcbiAgICAgICAgbW9kZTogJ2luc2Vuc2l0aXZlJ1xuICAgICAgfVxuICAgIH0pKVxuICB9XG59XG5cbi8vIERhdGUgcmFuZ2UgZmlsdGVyIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIGJ1aWxkRGF0ZVJhbmdlRmlsdGVyKFxuICBzdGFydERhdGU/OiBzdHJpbmcsXG4gIGVuZERhdGU/OiBzdHJpbmcsXG4gIGZpZWxkOiBzdHJpbmcgPSAnY3JlYXRlZEF0J1xuKSB7XG4gIGNvbnN0IGZpbHRlcjogYW55ID0ge31cblxuICBpZiAoc3RhcnREYXRlKSB7XG4gICAgZmlsdGVyW2ZpZWxkXSA9IHsgLi4uZmlsdGVyW2ZpZWxkXSwgZ3RlOiBuZXcgRGF0ZShzdGFydERhdGUpIH1cbiAgfVxuXG4gIGlmIChlbmREYXRlKSB7XG4gICAgY29uc3QgZW5kID0gbmV3IERhdGUoZW5kRGF0ZSlcbiAgICBlbmQuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KVxuICAgIGZpbHRlcltmaWVsZF0gPSB7IC4uLmZpbHRlcltmaWVsZF0sIGx0ZTogZW5kIH1cbiAgfVxuXG4gIHJldHVybiBPYmplY3Qua2V5cyhmaWx0ZXIpLmxlbmd0aCA+IDAgPyBmaWx0ZXIgOiB7fVxufVxuXG4vLyBWYWxpZGF0aW9uIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlUmVxdWlyZWRGaWVsZHMoZGF0YTogYW55LCBmaWVsZHM6IHN0cmluZ1tdKSB7XG4gIGNvbnN0IG1pc3NpbmcgPSBmaWVsZHMuZmlsdGVyKGZpZWxkID0+ICFkYXRhW2ZpZWxkXSlcblxuICBpZiAobWlzc2luZy5sZW5ndGggPiAwKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBNaXNzaW5nIHJlcXVpcmVkIGZpZWxkczogJHttaXNzaW5nLmpvaW4oJywgJyl9YClcbiAgfVxufVxuXG4vLyBTdG9yZSB2YWxpZGF0aW9uIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlU3RvcmVBY2Nlc3ModXNlcjogYW55KSB7XG4gIGlmICghdXNlci5zdG9yZUlkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhc3NvY2lhdGVkIHdpdGggYW55IHN0b3JlJylcbiAgfVxuICByZXR1cm4gdXNlci5zdG9yZUlkXG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY2hlY2tQZXJtaXNzaW9uIiwicHJpc21hIiwiY3JlYXRlUmVzcG9uc2UiLCJkYXRhIiwic3RhdHVzIiwianNvbiIsImNyZWF0ZUVycm9yUmVzcG9uc2UiLCJtZXNzYWdlIiwiZXJyb3IiLCJjcmVhdGVTdWNjZXNzUmVzcG9uc2UiLCJzdWNjZXNzIiwiY3JlYXRlUGFnaW5hdGVkUmVzcG9uc2UiLCJwYWdlIiwibGltaXQiLCJ0b3RhbCIsInBhZ2luYXRpb24iLCJ0b3RhbFBhZ2VzIiwiTWF0aCIsImNlaWwiLCJoYXNOZXh0IiwiaGFzUHJldiIsIndpdGhQZXJtaXNzaW9uIiwibW9kdWxlIiwicGVybWlzc2lvbiIsImhhbmRsZXIiLCJyZXF1ZXN0IiwiY29udGV4dCIsInBlcm1pc3Npb25DaGVjayIsInVzZXIiLCJjb25zb2xlIiwiY3JlYXRlQXVkaXRMb2ciLCJhY3Rpb24iLCJkZXRhaWxzIiwidXNlcklkIiwic3RvcmVJZCIsImF1ZGl0TG9nIiwiY3JlYXRlIiwiZ2V0UGFnaW5hdGlvblBhcmFtcyIsInVybCIsIlVSTCIsInBhcnNlSW50Iiwic2VhcmNoUGFyYW1zIiwiZ2V0Iiwic2VhcmNoIiwic29ydEJ5Iiwic29ydE9yZGVyIiwibWF4IiwibWluIiwic2tpcCIsImJ1aWxkU2VhcmNoRmlsdGVyIiwiZmllbGRzIiwiT1IiLCJtYXAiLCJmaWVsZCIsImNvbnRhaW5zIiwibW9kZSIsImJ1aWxkRGF0ZVJhbmdlRmlsdGVyIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsImZpbHRlciIsImd0ZSIsIkRhdGUiLCJlbmQiLCJzZXRIb3VycyIsImx0ZSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJ2YWxpZGF0ZVJlcXVpcmVkRmllbGRzIiwibWlzc2luZyIsIkVycm9yIiwiam9pbiIsInZhbGlkYXRlU3RvcmVBY2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n// Higher-order function for API route protection\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const authResult = await checkPermission(request, module, permission);\n            if (authResult.error) {\n                const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n                return createErrorResponse(authResult.error, authResult.status || 401);\n            }\n            return await handler(request, authResult.user);\n        } catch (error) {\n            console.error(\"Permission middleware error:\", error);\n            const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/role-storage.ts":
/*!*****************************!*\
  !*** ./lib/role-storage.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   predefinedRoles: () => (/* binding */ predefinedRoles),\n/* harmony export */   roleStorage: () => (/* binding */ roleStorage)\n/* harmony export */ });\n// Temporary in-memory storage for custom roles\n// In production, this would be replaced with database operations\n// Shared storage for custom roles\nlet customRoles = [];\nconst roleStorage = {\n    // Get all custom roles\n    getAll: ()=>{\n        return [\n            ...customRoles\n        ];\n    },\n    // Get a custom role by ID\n    getById: (id)=>{\n        return customRoles.find((role)=>role.id === id);\n    },\n    // Add a new custom role\n    add: (role)=>{\n        customRoles.push(role);\n    },\n    // Update a custom role\n    update: (id, updateData)=>{\n        const index = customRoles.findIndex((role)=>role.id === id);\n        if (index === -1) return null;\n        customRoles[index] = {\n            ...customRoles[index],\n            ...updateData\n        };\n        return customRoles[index];\n    },\n    // Delete a custom role\n    delete: (id)=>{\n        const initialLength = customRoles.length;\n        customRoles = customRoles.filter((role)=>role.id !== id);\n        return customRoles.length < initialLength;\n    },\n    // Clear all custom roles (for testing)\n    clear: ()=>{\n        customRoles = [];\n    }\n};\n// Predefined roles data\nconst predefinedRoles = [\n    {\n        id: \"founder\",\n        name: \"FOUNDER\",\n        description: \"System founder with complete access\",\n        isCustom: false,\n        moduleCount: 27,\n        permissionCount: 150,\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: \"super_admin\",\n        name: \"SUPER_ADMIN\",\n        description: \"Super administrator with extensive access\",\n        isCustom: false,\n        moduleCount: 25,\n        permissionCount: 120,\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: \"admin\",\n        name: \"ADMIN\",\n        description: \"Administrator with operational access\",\n        isCustom: false,\n        moduleCount: 20,\n        permissionCount: 80,\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: \"staff\",\n        name: \"STAFF\",\n        description: \"Staff member with limited access\",\n        isCustom: false,\n        moduleCount: 10,\n        permissionCount: 30,\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: \"distributor\",\n        name: \"DISTRIBUTOR\",\n        description: \"Distributor with specific access\",\n        isCustom: false,\n        moduleCount: 8,\n        permissionCount: 25,\n        createdAt: new Date().toISOString()\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/role-storage.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Froute&page=%2Fapi%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();