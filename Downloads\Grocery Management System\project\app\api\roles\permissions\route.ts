import { NextRequest, NextResponse } from 'next/server'
import { ROLE_PERMISSIONS } from '@/lib/permissions'
import { createErrorResponse, withPermission } from '@/lib/api-utils'

// GET /api/roles/permissions - Get role permission matrix
export const GET = withPermission('SETTINGS', 'READ', async (request: NextRequest, user: any) => {
  const url = new URL(request.url)
  const roleFilter = url.searchParams.get('role')
  const moduleFilter = url.searchParams.get('module')

  let filteredPermissions = ROLE_PERMISSIONS

  // Filter by role if specified
  if (roleFilter && roleFilter !== 'ALL') {
    filteredPermissions = filteredPermissions.filter(p => p.role === roleFilter)
  }

  // Filter by module if specified
  if (moduleFilter && moduleFilter !== 'ALL') {
    filteredPermissions = filteredPermissions.filter(p => p.module === moduleFilter)
  }

  // Transform data for better frontend consumption
  const permissionMatrix = filteredPermissions.map(permission => ({
    id: `${permission.role}_${permission.module}`,
    role: permission.role,
    module: permission.module,
    permissions: permission.permissions,
    canEdit: permission.role !== 'FOUNDER', // Founder permissions are immutable
    lastModified: new Date().toISOString()
  }))

  return NextResponse.json({
    success: true,
    data: permissionMatrix,
    message: 'Permission matrix fetched successfully'
  })
})

// PUT /api/roles/permissions - Update role permissions
export const PUT = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  const body = await request.json()
  const { role, module, permissions } = body

  if (!role || !module || !Array.isArray(permissions)) {
    return createErrorResponse('Role, module, and permissions array are required', 400)
  }

  // Prevent modification of FOUNDER role
  if (role === 'FOUNDER') {
    return createErrorResponse('Cannot modify FOUNDER role permissions', 403)
  }

  // Here you would update the permissions in database
  // For now, we'll simulate the update
  const updatedPermission = {
    role,
    module,
    permissions,
    lastModified: new Date().toISOString(),
    modifiedBy: user.id
  }

  console.log('Updating permissions:', updatedPermission)

  return NextResponse.json({
    success: true,
    data: updatedPermission,
    message: 'Permissions updated successfully'
  })
})

// POST /api/roles/permissions/bulk - Bulk update permissions
export const POST = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  const body = await request.json()
  const { updates } = body

  if (!Array.isArray(updates)) {
    return createErrorResponse('Updates array is required', 400)
  }

  // Process bulk updates
  const results = updates.map(update => {
    const { role, module, permissions } = update

    if (role === 'FOUNDER') {
      return {
        role,
        module,
        success: false,
        error: 'Cannot modify FOUNDER role permissions'
      }
    }

    // Here you would update in database
    console.log('Bulk updating:', { role, module, permissions })

    return {
      role,
      module,
      success: true,
      lastModified: new Date().toISOString(),
      modifiedBy: user.id
    }
  })

  return NextResponse.json({
    success: true,
    data: results,
    message: 'Bulk permissions update completed'
  })
})
