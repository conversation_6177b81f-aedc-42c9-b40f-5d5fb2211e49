"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/route";
exports.ids = ["app/api/analytics/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/analytics/route.ts */ \"(rsc)/./app/api/analytics/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/route\",\n        pathname: \"/api/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\api\\\\analytics\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/analytics/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/analytics/route.ts":
/*!************************************!*\
  !*** ./app/api/analytics/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst analyticsRequestSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    metrics: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"REVENUE_TRENDS\",\n        \"CUSTOMER_ACQUISITION\",\n        \"PRODUCT_PERFORMANCE\",\n        \"INVENTORY_TURNOVER\",\n        \"PROFIT_MARGINS\",\n        \"SEASONAL_ANALYSIS\",\n        \"COHORT_ANALYSIS\",\n        \"FORECASTING\"\n    ])),\n    period: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        startDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        endDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        granularity: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n            \"HOUR\",\n            \"DAY\",\n            \"WEEK\",\n            \"MONTH\",\n            \"QUARTER\"\n        ]).default(\"DAY\")\n    }),\n    compareWith: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        enabled: zod__WEBPACK_IMPORTED_MODULE_2__.z.boolean().default(false),\n        startDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n        endDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n    }).optional(),\n    filters: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        categories: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).optional(),\n        products: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).optional(),\n        customers: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).optional(),\n        regions: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).optional()\n    }).optional()\n});\n// GET /api/analytics - Get business intelligence analytics\nconst GET = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"ANALYTICS\", \"READ\", async (request, user)=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { searchParams } = new URL(request.url);\n    const metric = searchParams.get(\"metric\");\n    const startDate = searchParams.get(\"startDate\");\n    const endDate = searchParams.get(\"endDate\");\n    const granularity = searchParams.get(\"granularity\") || \"DAY\";\n    if (!metric || !startDate || !endDate) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Missing required parameters: metric, startDate, endDate\", 400);\n    }\n    try {\n        let analyticsData = {};\n        switch(metric){\n            case \"REVENUE_TRENDS\":\n                analyticsData = await getRevenueTrends(storeId, new Date(startDate), new Date(endDate), granularity);\n                break;\n            case \"CUSTOMER_ACQUISITION\":\n                analyticsData = await getCustomerAcquisition(storeId, new Date(startDate), new Date(endDate), granularity);\n                break;\n            case \"PRODUCT_PERFORMANCE\":\n                analyticsData = await getProductPerformance(storeId, new Date(startDate), new Date(endDate));\n                break;\n            case \"INVENTORY_TURNOVER\":\n                analyticsData = await getInventoryTurnover(storeId, new Date(startDate), new Date(endDate));\n                break;\n            case \"PROFIT_MARGINS\":\n                analyticsData = await getProfitMargins(storeId, new Date(startDate), new Date(endDate));\n                break;\n            case \"SEASONAL_ANALYSIS\":\n                analyticsData = await getSeasonalAnalysis(storeId, new Date(startDate), new Date(endDate));\n                break;\n            case \"COHORT_ANALYSIS\":\n                analyticsData = await getCohortAnalysis(storeId, new Date(startDate), new Date(endDate));\n                break;\n            case \"FORECASTING\":\n                analyticsData = await getForecasting(storeId, new Date(startDate), new Date(endDate));\n                break;\n            default:\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Invalid metric type\", 400);\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)({\n            metric,\n            period: {\n                startDate,\n                endDate,\n                granularity\n            },\n            data: analyticsData,\n            generatedAt: new Date()\n        });\n    } catch (error) {\n        console.error(\"Error generating analytics:\", error);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to generate analytics\", 500);\n    }\n});\n// POST /api/analytics - Generate comprehensive analytics report\nconst POST = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"ANALYTICS\", \"CREATE\", async (request, user)=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const body = await request.json();\n    const data = analyticsRequestSchema.parse(body);\n    try {\n        const results = {};\n        const startDate = new Date(data.period.startDate);\n        const endDate = new Date(data.period.endDate);\n        // Generate analytics for each requested metric\n        for (const metric of data.metrics){\n            switch(metric){\n                case \"REVENUE_TRENDS\":\n                    results[metric] = await getRevenueTrends(storeId, startDate, endDate, data.period.granularity);\n                    break;\n                case \"CUSTOMER_ACQUISITION\":\n                    results[metric] = await getCustomerAcquisition(storeId, startDate, endDate, data.period.granularity);\n                    break;\n                case \"PRODUCT_PERFORMANCE\":\n                    results[metric] = await getProductPerformance(storeId, startDate, endDate);\n                    break;\n                case \"INVENTORY_TURNOVER\":\n                    results[metric] = await getInventoryTurnover(storeId, startDate, endDate);\n                    break;\n                case \"PROFIT_MARGINS\":\n                    results[metric] = await getProfitMargins(storeId, startDate, endDate);\n                    break;\n                case \"SEASONAL_ANALYSIS\":\n                    results[metric] = await getSeasonalAnalysis(storeId, startDate, endDate);\n                    break;\n                case \"COHORT_ANALYSIS\":\n                    results[metric] = await getCohortAnalysis(storeId, startDate, endDate);\n                    break;\n                case \"FORECASTING\":\n                    results[metric] = await getForecasting(storeId, startDate, endDate);\n                    break;\n            }\n        }\n        // Add comparison data if requested\n        if (data.compareWith?.enabled && data.compareWith.startDate && data.compareWith.endDate) {\n            const compareStartDate = new Date(data.compareWith.startDate);\n            const compareEndDate = new Date(data.compareWith.endDate);\n            results.comparison = {};\n            for (const metric of data.metrics){\n                // Generate comparison data for each metric\n                results.comparison[metric] = await generateComparisonData(storeId, metric, compareStartDate, compareEndDate, data.period.granularity);\n            }\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)({\n            metrics: data.metrics,\n            period: data.period,\n            data: results,\n            generatedAt: new Date()\n        }, \"Analytics generated successfully\");\n    } catch (error) {\n        console.error(\"Error generating comprehensive analytics:\", error);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to generate analytics\", 500);\n    }\n});\n// Analytics helper functions\nasync function getRevenueTrends(storeId, startDate, endDate, granularity) {\n    const sales = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n        where: {\n            storeId,\n            createdAt: {\n                gte: startDate,\n                lte: endDate\n            },\n            status: \"COMPLETED\"\n        },\n        select: {\n            createdAt: true,\n            total: true,\n            taxAmount: true,\n            discount: true\n        },\n        orderBy: {\n            createdAt: \"asc\"\n        }\n    });\n    const trends = groupByPeriod(sales, granularity, (items)=>({\n            revenue: items.reduce((sum, sale)=>sum + sale.total, 0),\n            tax: items.reduce((sum, sale)=>sum + sale.taxAmount, 0),\n            discount: items.reduce((sum, sale)=>sum + sale.discount, 0),\n            transactions: items.length,\n            averageOrderValue: items.length > 0 ? items.reduce((sum, sale)=>sum + sale.total, 0) / items.length : 0\n        }));\n    return {\n        trends,\n        summary: {\n            totalRevenue: sales.reduce((sum, sale)=>sum + sale.total, 0),\n            totalTransactions: sales.length,\n            averageOrderValue: sales.length > 0 ? sales.reduce((sum, sale)=>sum + sale.total, 0) / sales.length : 0,\n            growthRate: calculateGrowthRate(trends)\n        }\n    };\n}\nasync function getCustomerAcquisition(storeId, startDate, endDate, granularity) {\n    const customers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.customer.findMany({\n        where: {\n            storeId,\n            createdAt: {\n                gte: startDate,\n                lte: endDate\n            }\n        },\n        select: {\n            createdAt: true,\n            totalPurchases: true\n        },\n        orderBy: {\n            createdAt: \"asc\"\n        }\n    });\n    const acquisitionTrends = groupByPeriod(customers, granularity, (items)=>({\n            newCustomers: items.length,\n            totalValue: items.reduce((sum, customer)=>sum + customer.totalPurchases, 0),\n            averageValue: items.length > 0 ? items.reduce((sum, customer)=>sum + customer.totalPurchases, 0) / items.length : 0\n        }));\n    return {\n        trends: acquisitionTrends,\n        summary: {\n            totalNewCustomers: customers.length,\n            acquisitionRate: calculateAcquisitionRate(acquisitionTrends),\n            customerLifetimeValue: customers.length > 0 ? customers.reduce((sum, customer)=>sum + customer.totalPurchases, 0) / customers.length : 0\n        }\n    };\n}\nasync function getProductPerformance(storeId, startDate, endDate) {\n    const saleItems = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.saleItem.findMany({\n        where: {\n            sale: {\n                storeId,\n                createdAt: {\n                    gte: startDate,\n                    lte: endDate\n                },\n                status: \"COMPLETED\"\n            }\n        },\n        include: {\n            product: {\n                select: {\n                    id: true,\n                    name: true,\n                    sku: true,\n                    costPrice: true,\n                    sellingPrice: true,\n                    category: {\n                        select: {\n                            name: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    const productStats = saleItems.reduce((acc, item)=>{\n        const productId = item.productId;\n        if (!acc[productId]) {\n            acc[productId] = {\n                product: item.product,\n                totalQuantity: 0,\n                totalRevenue: 0,\n                totalCost: 0,\n                totalProfit: 0,\n                salesCount: 0\n            };\n        }\n        const cost = item.quantity * item.product.costPrice;\n        const profit = item.totalPrice - cost;\n        acc[productId].totalQuantity += item.quantity;\n        acc[productId].totalRevenue += item.totalPrice;\n        acc[productId].totalCost += cost;\n        acc[productId].totalProfit += profit;\n        acc[productId].salesCount += 1;\n        return acc;\n    }, {});\n    const performanceData = Object.values(productStats).map((stats)=>({\n            ...stats,\n            profitMargin: stats.totalRevenue > 0 ? stats.totalProfit / stats.totalRevenue * 100 : 0,\n            averageOrderQuantity: stats.salesCount > 0 ? stats.totalQuantity / stats.salesCount : 0\n        }));\n    return {\n        topByRevenue: performanceData.sort((a, b)=>b.totalRevenue - a.totalRevenue).slice(0, 10),\n        topByQuantity: performanceData.sort((a, b)=>b.totalQuantity - a.totalQuantity).slice(0, 10),\n        topByProfit: performanceData.sort((a, b)=>b.totalProfit - a.totalProfit).slice(0, 10),\n        lowPerformers: performanceData.sort((a, b)=>a.totalRevenue - b.totalRevenue).slice(0, 10)\n    };\n}\nasync function getInventoryTurnover(storeId, startDate, endDate) {\n    const [inventory, saleItems] = await Promise.all([\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.inventory.findMany({\n            where: {\n                storeId\n            },\n            include: {\n                product: {\n                    select: {\n                        id: true,\n                        name: true,\n                        costPrice: true\n                    }\n                }\n            }\n        }),\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.saleItem.findMany({\n            where: {\n                sale: {\n                    storeId,\n                    createdAt: {\n                        gte: startDate,\n                        lte: endDate\n                    },\n                    status: \"COMPLETED\"\n                }\n            },\n            select: {\n                productId: true,\n                quantity: true\n            }\n        })\n    ]);\n    const turnoverData = inventory.map((inv)=>{\n        const soldQuantity = saleItems.filter((item)=>item.productId === inv.productId).reduce((sum, item)=>sum + item.quantity, 0);\n        const averageInventory = inv.quantity + soldQuantity / 2 // Simplified calculation\n        ;\n        const turnoverRate = averageInventory > 0 ? soldQuantity / averageInventory : 0;\n        return {\n            productId: inv.productId,\n            productName: inv.product.name,\n            currentStock: inv.quantity,\n            soldQuantity,\n            turnoverRate,\n            daysToSellOut: turnoverRate > 0 ? 365 / turnoverRate : Infinity,\n            stockValue: inv.quantity * inv.product.costPrice\n        };\n    });\n    return {\n        products: turnoverData.sort((a, b)=>b.turnoverRate - a.turnoverRate),\n        summary: {\n            averageTurnoverRate: turnoverData.reduce((sum, item)=>sum + item.turnoverRate, 0) / turnoverData.length,\n            fastMoving: turnoverData.filter((item)=>item.turnoverRate > 12).length,\n            slowMoving: turnoverData.filter((item)=>item.turnoverRate < 2).length,\n            totalStockValue: turnoverData.reduce((sum, item)=>sum + item.stockValue, 0)\n        }\n    };\n}\nasync function getProfitMargins(storeId, startDate, endDate) {\n    const saleItems = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.saleItem.findMany({\n        where: {\n            sale: {\n                storeId,\n                createdAt: {\n                    gte: startDate,\n                    lte: endDate\n                },\n                status: \"COMPLETED\"\n            }\n        },\n        include: {\n            product: {\n                select: {\n                    costPrice: true,\n                    category: {\n                        select: {\n                            name: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    const marginAnalysis = saleItems.reduce((acc, item)=>{\n        const cost = item.quantity * item.product.costPrice;\n        const profit = item.totalPrice - cost;\n        const margin = item.totalPrice > 0 ? profit / item.totalPrice * 100 : 0;\n        const category = item.product.category.name;\n        if (!acc[category]) {\n            acc[category] = {\n                totalRevenue: 0,\n                totalCost: 0,\n                totalProfit: 0,\n                itemCount: 0\n            };\n        }\n        acc[category].totalRevenue += item.totalPrice;\n        acc[category].totalCost += cost;\n        acc[category].totalProfit += profit;\n        acc[category].itemCount += 1;\n        return acc;\n    }, {});\n    const categoryMargins = Object.entries(marginAnalysis).map(([category, data])=>({\n            category,\n            ...data,\n            profitMargin: data.totalRevenue > 0 ? data.totalProfit / data.totalRevenue * 100 : 0\n        }));\n    return {\n        byCategory: categoryMargins.sort((a, b)=>b.profitMargin - a.profitMargin),\n        overall: {\n            totalRevenue: categoryMargins.reduce((sum, cat)=>sum + cat.totalRevenue, 0),\n            totalCost: categoryMargins.reduce((sum, cat)=>sum + cat.totalCost, 0),\n            totalProfit: categoryMargins.reduce((sum, cat)=>sum + cat.totalProfit, 0),\n            overallMargin: categoryMargins.reduce((sum, cat)=>sum + cat.totalRevenue, 0) > 0 ? categoryMargins.reduce((sum, cat)=>sum + cat.totalProfit, 0) / categoryMargins.reduce((sum, cat)=>sum + cat.totalRevenue, 0) * 100 : 0\n        }\n    };\n}\nasync function getSeasonalAnalysis(storeId, startDate, endDate) {\n    const sales = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n        where: {\n            storeId,\n            createdAt: {\n                gte: startDate,\n                lte: endDate\n            },\n            status: \"COMPLETED\"\n        },\n        select: {\n            createdAt: true,\n            total: true\n        }\n    });\n    const seasonalData = sales.reduce((acc, sale)=>{\n        const date = new Date(sale.createdAt);\n        const month = date.getMonth();\n        const quarter = Math.floor(month / 3) + 1;\n        const dayOfWeek = date.getDay();\n        const hour = date.getHours();\n        // Monthly analysis\n        if (!acc.monthly[month]) {\n            acc.monthly[month] = {\n                sales: 0,\n                revenue: 0\n            };\n        }\n        acc.monthly[month].sales += 1;\n        acc.monthly[month].revenue += sale.total;\n        // Quarterly analysis\n        if (!acc.quarterly[quarter]) {\n            acc.quarterly[quarter] = {\n                sales: 0,\n                revenue: 0\n            };\n        }\n        acc.quarterly[quarter].sales += 1;\n        acc.quarterly[quarter].revenue += sale.total;\n        // Day of week analysis\n        if (!acc.dayOfWeek[dayOfWeek]) {\n            acc.dayOfWeek[dayOfWeek] = {\n                sales: 0,\n                revenue: 0\n            };\n        }\n        acc.dayOfWeek[dayOfWeek].sales += 1;\n        acc.dayOfWeek[dayOfWeek].revenue += sale.total;\n        // Hourly analysis\n        if (!acc.hourly[hour]) {\n            acc.hourly[hour] = {\n                sales: 0,\n                revenue: 0\n            };\n        }\n        acc.hourly[hour].sales += 1;\n        acc.hourly[hour].revenue += sale.total;\n        return acc;\n    }, {\n        monthly: {},\n        quarterly: {},\n        dayOfWeek: {},\n        hourly: {}\n    });\n    return seasonalData;\n}\nasync function getCohortAnalysis(storeId, startDate, endDate) {\n    // Simplified cohort analysis - would need more complex implementation for full cohort tracking\n    const customers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.customer.findMany({\n        where: {\n            storeId,\n            createdAt: {\n                gte: startDate,\n                lte: endDate\n            }\n        },\n        include: {\n            sales: {\n                where: {\n                    status: \"COMPLETED\"\n                },\n                select: {\n                    createdAt: true,\n                    total: true\n                }\n            }\n        }\n    });\n    const cohorts = customers.reduce((acc, customer)=>{\n        const cohortMonth = customer.createdAt.toISOString().substring(0, 7) // YYYY-MM\n        ;\n        if (!acc[cohortMonth]) {\n            acc[cohortMonth] = {\n                customers: 0,\n                totalRevenue: 0,\n                averageOrderValue: 0,\n                retentionRate: 0\n            };\n        }\n        acc[cohortMonth].customers += 1;\n        const customerRevenue = customer.sales.reduce((sum, sale)=>sum + sale.total, 0);\n        acc[cohortMonth].totalRevenue += customerRevenue;\n        return acc;\n    }, {});\n    return Object.entries(cohorts).map(([month, data])=>({\n            cohortMonth: month,\n            ...data,\n            averageOrderValue: data.customers > 0 ? data.totalRevenue / data.customers : 0\n        }));\n}\nasync function getForecasting(storeId, startDate, endDate) {\n    // Simple linear regression forecasting based on historical trends\n    const sales = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n        where: {\n            storeId,\n            createdAt: {\n                gte: startDate,\n                lte: endDate\n            },\n            status: \"COMPLETED\"\n        },\n        select: {\n            createdAt: true,\n            total: true\n        },\n        orderBy: {\n            createdAt: \"asc\"\n        }\n    });\n    const dailyRevenue = groupByPeriod(sales, \"DAY\", (items)=>items.reduce((sum, sale)=>sum + sale.total, 0));\n    // Simple trend calculation\n    const trend = calculateTrend(dailyRevenue.map((d)=>d.value));\n    const nextPeriodForecast = generateForecast(dailyRevenue, trend, 30) // 30 days forecast\n    ;\n    return {\n        historicalTrend: trend,\n        forecast: nextPeriodForecast,\n        confidence: calculateForecastConfidence(dailyRevenue)\n    };\n}\n// Utility functions\nfunction groupByPeriod(data, granularity, aggregator) {\n    const groups = new Map();\n    data.forEach((item)=>{\n        const date = new Date(item.createdAt);\n        let key;\n        switch(granularity){\n            case \"HOUR\":\n                key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")} ${String(date.getHours()).padStart(2, \"0\")}:00`;\n                break;\n            case \"WEEK\":\n                const weekStart = new Date(date);\n                weekStart.setDate(date.getDate() - date.getDay());\n                key = weekStart.toISOString().split(\"T\")[0];\n                break;\n            case \"MONTH\":\n                key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}`;\n                break;\n            case \"QUARTER\":\n                const quarter = Math.floor(date.getMonth() / 3) + 1;\n                key = `${date.getFullYear()}-Q${quarter}`;\n                break;\n            default:\n                key = date.toISOString().split(\"T\")[0];\n        }\n        if (!groups.has(key)) {\n            groups.set(key, []);\n        }\n        groups.get(key).push(item);\n    });\n    return Array.from(groups.entries()).map(([period, items])=>({\n            period,\n            ...aggregator(items)\n        })).sort((a, b)=>a.period.localeCompare(b.period));\n}\nfunction calculateGrowthRate(trends) {\n    if (trends.length < 2) return 0;\n    const firstValue = trends[0].revenue;\n    const lastValue = trends[trends.length - 1].revenue;\n    return firstValue > 0 ? (lastValue - firstValue) / firstValue * 100 : 0;\n}\nfunction calculateAcquisitionRate(trends) {\n    if (trends.length === 0) return 0;\n    return trends.reduce((sum, trend)=>sum + trend.newCustomers, 0) / trends.length;\n}\nfunction calculateTrend(values) {\n    if (values.length < 2) return 0;\n    const n = values.length;\n    const sumX = n * (n - 1) / 2;\n    const sumY = values.reduce((sum, val)=>sum + val, 0);\n    const sumXY = values.reduce((sum, val, index)=>sum + index * val, 0);\n    const sumXX = n * (n - 1) * (2 * n - 1) / 6;\n    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);\n}\nfunction generateForecast(historicalData, trend, days) {\n    const forecast = [];\n    const lastValue = historicalData[historicalData.length - 1]?.value || 0;\n    for(let i = 1; i <= days; i++){\n        const forecastValue = lastValue + trend * i;\n        forecast.push({\n            period: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split(\"T\")[0],\n            value: Math.max(0, forecastValue)\n        });\n    }\n    return forecast;\n}\nfunction calculateForecastConfidence(historicalData) {\n    // Simplified confidence calculation based on data consistency\n    if (historicalData.length < 7) return 0.3;\n    const values = historicalData.map((d)=>d.value);\n    const mean = values.reduce((sum, val)=>sum + val, 0) / values.length;\n    const variance = values.reduce((sum, val)=>sum + Math.pow(val - mean, 2), 0) / values.length;\n    const coefficient = variance > 0 ? Math.sqrt(variance) / mean : 0;\n    return Math.max(0.1, Math.min(0.9, 1 - coefficient));\n}\nasync function generateComparisonData(storeId, metric, startDate, endDate, granularity) {\n    // Generate comparison data for the specified metric and period\n    switch(metric){\n        case \"REVENUE_TRENDS\":\n            return await getRevenueTrends(storeId, startDate, endDate, granularity);\n        case \"CUSTOMER_ACQUISITION\":\n            return await getCustomerAcquisition(storeId, startDate, endDate, granularity);\n        default:\n            return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/analytics/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request, context)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user, context);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n// Higher-order function for API route protection\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const authResult = await checkPermission(request, module, permission);\n            if (authResult.error) {\n                const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n                return createErrorResponse(authResult.error, authResult.status || 401);\n            }\n            return await handler(request, authResult.user);\n        } catch (error) {\n            console.error(\"Permission middleware error:\", error);\n            const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();