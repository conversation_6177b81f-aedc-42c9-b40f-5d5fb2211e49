"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/roles/permissions/route";
exports.ids = ["app/api/roles/permissions/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Fpermissions%2Froute&page=%2Fapi%2Froles%2Fpermissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Fpermissions%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Fpermissions%2Froute&page=%2Fapi%2Froles%2Fpermissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Fpermissions%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_roles_permissions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/roles/permissions/route.ts */ \"(rsc)/./app/api/roles/permissions/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/roles/permissions/route\",\n        pathname: \"/api/roles/permissions\",\n        filename: \"route\",\n        bundlePath: \"app/api/roles/permissions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\api\\\\roles\\\\permissions\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_roles_permissions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/roles/permissions/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Fpermissions%2Froute&page=%2Fapi%2Froles%2Fpermissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Fpermissions%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/roles/permissions/route.ts":
/*!********************************************!*\
  !*** ./app/api/roles/permissions/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./lib/permissions.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n\n\n\n// GET /api/roles/permissions - Get role permission matrix\nconst GET = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"READ\", async (request, user)=>{\n    const url = new URL(request.url);\n    const roleFilter = url.searchParams.get(\"role\");\n    const moduleFilter = url.searchParams.get(\"module\");\n    let filteredPermissions = _lib_permissions__WEBPACK_IMPORTED_MODULE_1__.ROLE_PERMISSIONS;\n    // Filter by role if specified\n    if (roleFilter && roleFilter !== \"ALL\") {\n        filteredPermissions = filteredPermissions.filter((p)=>p.role === roleFilter);\n    }\n    // Filter by module if specified\n    if (moduleFilter && moduleFilter !== \"ALL\") {\n        filteredPermissions = filteredPermissions.filter((p)=>p.module === moduleFilter);\n    }\n    // Transform data for better frontend consumption\n    const permissionMatrix = filteredPermissions.map((permission)=>({\n            id: `${permission.role}_${permission.module}`,\n            role: permission.role,\n            module: permission.module,\n            permissions: permission.permissions,\n            canEdit: permission.role !== \"FOUNDER\",\n            lastModified: new Date().toISOString()\n        }));\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data: permissionMatrix,\n        message: \"Permission matrix fetched successfully\"\n    });\n});\n// PUT /api/roles/permissions - Update role permissions\nconst PUT = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"UPDATE\", async (request, user)=>{\n    const body = await request.json();\n    const { role, module, permissions } = body;\n    if (!role || !module || !Array.isArray(permissions)) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Role, module, and permissions array are required\", 400);\n    }\n    // Prevent modification of FOUNDER role\n    if (role === \"FOUNDER\") {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Cannot modify FOUNDER role permissions\", 403);\n    }\n    // Here you would update the permissions in database\n    // For now, we'll simulate the update\n    const updatedPermission = {\n        role,\n        module,\n        permissions,\n        lastModified: new Date().toISOString(),\n        modifiedBy: user.id\n    };\n    console.log(\"Updating permissions:\", updatedPermission);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data: updatedPermission,\n        message: \"Permissions updated successfully\"\n    });\n});\n// POST /api/roles/permissions/bulk - Bulk update permissions\nconst POST = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.withPermission)(\"SETTINGS\", \"UPDATE\", async (request, user)=>{\n    const body = await request.json();\n    const { updates } = body;\n    if (!Array.isArray(updates)) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(\"Updates array is required\", 400);\n    }\n    // Process bulk updates\n    const results = updates.map((update)=>{\n        const { role, module, permissions } = update;\n        if (role === \"FOUNDER\") {\n            return {\n                role,\n                module,\n                success: false,\n                error: \"Cannot modify FOUNDER role permissions\"\n            };\n        }\n        // Here you would update in database\n        console.log(\"Bulk updating:\", {\n            role,\n            module,\n            permissions\n        });\n        return {\n            role,\n            module,\n            success: true,\n            lastModified: new Date().toISOString(),\n            modifiedBy: user.id\n        };\n    });\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data: results,\n        message: \"Bulk permissions update completed\"\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/roles/permissions/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request, context)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user, context);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n// Higher-order function for API route protection\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const authResult = await checkPermission(request, module, permission);\n            if (authResult.error) {\n                const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n                return createErrorResponse(authResult.error, authResult.status || 401);\n            }\n            return await handler(request, authResult.user);\n        } catch (error) {\n            console.error(\"Permission middleware error:\", error);\n            const { createErrorResponse } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_api-utils_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./api-utils */ \"(rsc)/./lib/api-utils.ts\"));\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getModulePermissions: () => (/* binding */ getModulePermissions),\n/* harmony export */   getUserModules: () => (/* binding */ getUserModules),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\nconst ROLE_PERMISSIONS = [\n    // FOUNDER - Full access\n    {\n        role: \"FOUNDER\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"ROLE\",\n        permissions: [\n            \"MANAGE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"STORE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUBSCRIPTION\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"ANALYTICS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"ALERTS\",\n        permissions: [\n            \"READ\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"DISTRIBUTOR\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"CREATE\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUBSCRIPTION\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"AUDIT\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SECURITY\",\n        permissions: [\n            \"READ\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"FINANCIAL\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    // Add missing modules for FOUNDER - Full access to ALL modules\n    {\n        role: \"FOUNDER\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"IMPORT\",\n            \"EXPORT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"EXPORT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"APPROVE\",\n            \"REJECT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"APPROVE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"EXPORT\",\n            \"PRINT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"EXPORT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"EXPENSE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"EXPORT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"PAYMENT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"B2B\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"APPROVE\",\n            \"REJECT\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"TAX\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ACCESS_SETTINGS\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"DOWNLOAD\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"FEEDBACK\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    // SUPER_ADMIN\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"STORE\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"READ\",\n            \"CREATE\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"ANALYTICS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"ALERTS\",\n        permissions: [\n            \"READ\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"DISTRIBUTOR\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"CREATE\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SECURITY\",\n        permissions: [\n            \"READ\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"FINANCIAL\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    // Add missing modules for SUPER_ADMIN - Limited access (no MANAGE permissions)\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"IMPORT\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"APPROVE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"EXPENSE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"PAYMENT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"B2B\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"TAX\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\",\n            \"UPDATE\",\n            \"DOWNLOAD\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"FEEDBACK\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SUBSCRIPTION\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"AUDIT\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    // ADMIN\n    {\n        role: \"ADMIN\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"IMPORT\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DISTRIBUTOR\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"EXPENSE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PAYMENT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"B2B\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"TAX\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\",\n            \"DOWNLOAD\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"FEEDBACK\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"ANALYTICS\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"ALERTS\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SECURITY\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"FINANCIAL\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"AUDIT\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // STAFF\n    {\n        role: \"STAFF\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // Add missing modules for STAFF - Very limited access (mostly READ only)\n    {\n        role: \"STAFF\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // DISTRIBUTOR\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    }\n];\nfunction hasPermission(userRole, module, permission) {\n    const rolePermissions = ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermissions.some((rp)=>rp.permissions.includes(permission));\n}\nfunction getUserModules(userRole) {\n    return ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole).map((rp)=>rp.module);\n}\nfunction getModulePermissions(userRole, module) {\n    const rolePermission = ROLE_PERMISSIONS.find((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermission?.permissions || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Froles%2Fpermissions%2Froute&page=%2Fapi%2Froles%2Fpermissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froles%2Fpermissions%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();