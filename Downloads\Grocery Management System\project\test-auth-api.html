<!DOCTYPE html>
<html>
<head>
    <title>Test Role API with Auth</title>
</head>
<body>
    <h1>Test Role API with Authentication</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing API...</p>';
            
            try {
                // First, let's try to get the token from cookies (like the frontend does)
                const token = getCookie('token');
                console.log('Token from cookie:', token ? 'Found' : 'Not found');
                
                if (!token) {
                    resultsDiv.innerHTML = '<p style="color: red;">No authentication token found in cookies. Please login first.</p>';
                    return;
                }
                
                // Test GET /api/roles
                console.log('Testing GET /api/roles...');
                const response = await fetch('/api/roles', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <h3>✅ API Test Successful!</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Success:</strong> ${data.success}</p>
                        <p><strong>Total Roles:</strong> ${data.data?.length || 0}</p>
                        <p><strong>Custom Roles:</strong> ${data.data?.filter(r => r.isCustom).length || 0}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <h3>❌ API Test Failed</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                resultsDiv.innerHTML = `
                    <h3>❌ Test Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        // Function to get cookie value
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
    </script>
</body>
</html>
