import { Role, Module, Permission, RolePermission } from './types'

export const ROLE_PERMISSIONS: RolePermission[] = [
  // FOUNDER - Full access
  {
    role: 'FOUNDER',
    module: 'USER',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'ASSIGN', '<PERSON>NA<PERSON>']
  },
  {
    role: 'FOUNDER',
    module: 'ROLE',
    permissions: ['MANA<PERSON>', 'ACCESS_SETTINGS']
  },
  {
    role: 'FOUNDER',
    module: 'STORE',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'SETTINGS',
    permissions: ['ACCESS_SETTINGS', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'REPORTS',
    permissions: ['READ', 'EXPORT', 'PRINT']
  },
  {
    role: 'FOUNDER',
    module: 'SUBSCRIPTION',
    permissions: ['READ', 'UPDATE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'AUDIT_LOG',
    permissions: ['READ', 'EXPORT']
  },
  {
    role: 'FOUNDER',
    module: 'DASHBOARD',
    permissions: ['READ']
  },
  {
    role: 'FOUNDER',
    module: 'ANALYTICS',
    permissions: ['READ', 'EXPORT']
  },
  {
    role: 'FOUNDER',
    module: 'ALERTS',
    permissions: ['READ', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'DISTRIBUTOR',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'BILLING',
    permissions: ['READ', 'CREATE', 'UPDATE', 'DELETE', 'EXPORT']
  },
  {
    role: 'FOUNDER',
    module: 'SUBSCRIPTION',
    permissions: ['READ', 'UPDATE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'AUDIT',
    permissions: ['READ', 'EXPORT']
  },
  {
    role: 'FOUNDER',
    module: 'SECURITY',
    permissions: ['READ', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'FINANCIAL',
    permissions: ['READ', 'EXPORT']
  },
  // Add missing modules for FOUNDER - Full access to ALL modules
  {
    role: 'FOUNDER',
    module: 'PRODUCT',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'IMPORT', 'EXPORT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'CATEGORY',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'INVENTORY',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'EXPORT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'PURCHASE',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'APPROVE', 'REJECT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'PURCHASE_RETURN',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'APPROVE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'SALES',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'EXPORT', 'PRINT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'SALES_RETURN',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'CUSTOMER',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'EXPORT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'SUPPLIER',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'EXPENSE',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'EXPORT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'PAYMENT',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'B2B',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'APPROVE', 'REJECT', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'TAX',
    permissions: ['READ', 'UPDATE', 'DELETE', 'ACCESS_SETTINGS', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'NOTIFICATION',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'DOCUMENT',
    permissions: ['UPLOAD', 'READ', 'UPDATE', 'DELETE', 'DOWNLOAD', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'FEEDBACK',
    permissions: ['READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  {
    role: 'FOUNDER',
    module: 'SUPPORT',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },

  // SUPER_ADMIN
  {
    role: 'SUPER_ADMIN',
    module: 'USER',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'ASSIGN']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'STORE',
    permissions: ['READ', 'UPDATE', 'MANAGE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SETTINGS',
    permissions: ['ACCESS_SETTINGS', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'REPORTS',
    permissions: ['READ', 'EXPORT', 'PRINT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'AUDIT_LOG',
    permissions: ['READ', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SUPPORT',
    permissions: ['READ', 'CREATE', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'DASHBOARD',
    permissions: ['READ']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'ANALYTICS',
    permissions: ['READ', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'ALERTS',
    permissions: ['READ', 'MANAGE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'DISTRIBUTOR',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'BILLING',
    permissions: ['READ', 'CREATE', 'UPDATE', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SECURITY',
    permissions: ['READ', 'MANAGE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'FINANCIAL',
    permissions: ['READ', 'EXPORT']
  },
  // Add missing modules for SUPER_ADMIN - Limited access (no MANAGE permissions)
  {
    role: 'SUPER_ADMIN',
    module: 'PRODUCT',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'IMPORT', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'CATEGORY',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'INVENTORY',
    permissions: ['READ', 'UPDATE', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'PURCHASE',
    permissions: ['CREATE', 'READ', 'UPDATE', 'APPROVE', 'REJECT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'PURCHASE_RETURN',
    permissions: ['CREATE', 'READ', 'APPROVE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SALES',
    permissions: ['CREATE', 'READ', 'UPDATE', 'EXPORT', 'PRINT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SALES_RETURN',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'CUSTOMER',
    permissions: ['CREATE', 'READ', 'UPDATE', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SUPPLIER',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'EXPENSE',
    permissions: ['CREATE', 'READ', 'UPDATE', 'EXPORT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'PAYMENT',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'B2B',
    permissions: ['CREATE', 'READ', 'UPDATE', 'APPROVE', 'REJECT']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'TAX',
    permissions: ['READ', 'UPDATE', 'ACCESS_SETTINGS']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'NOTIFICATION',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'DOCUMENT',
    permissions: ['UPLOAD', 'READ', 'UPDATE', 'DOWNLOAD']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'FEEDBACK',
    permissions: ['READ', 'UPDATE']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'SUBSCRIPTION',
    permissions: ['READ']
  },
  {
    role: 'SUPER_ADMIN',
    module: 'AUDIT',
    permissions: ['READ', 'EXPORT']
  },

  // ADMIN
  {
    role: 'ADMIN',
    module: 'PRODUCT',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'IMPORT', 'EXPORT']
  },
  {
    role: 'ADMIN',
    module: 'CATEGORY',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE']
  },
  {
    role: 'ADMIN',
    module: 'INVENTORY',
    permissions: ['READ', 'UPDATE', 'EXPORT']
  },
  {
    role: 'ADMIN',
    module: 'PURCHASE',
    permissions: ['CREATE', 'READ', 'UPDATE', 'APPROVE', 'REJECT']
  },
  {
    role: 'ADMIN',
    module: 'PURCHASE_RETURN',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'ADMIN',
    module: 'SALES',
    permissions: ['CREATE', 'READ', 'EXPORT', 'PRINT']
  },
  {
    role: 'ADMIN',
    module: 'SALES_RETURN',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'ADMIN',
    module: 'CUSTOMER',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'ADMIN',
    module: 'SUPPLIER',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'ADMIN',
    module: 'DISTRIBUTOR',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'ADMIN',
    module: 'EXPENSE',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'ADMIN',
    module: 'PAYMENT',
    permissions: ['CREATE', 'READ', 'UPDATE']
  },
  {
    role: 'ADMIN',
    module: 'B2B',
    permissions: ['CREATE', 'READ', 'UPDATE', 'APPROVE', 'REJECT']
  },
  {
    role: 'ADMIN',
    module: 'BILLING',
    permissions: ['READ', 'PRINT']
  },
  {
    role: 'ADMIN',
    module: 'TAX',
    permissions: ['READ', 'UPDATE', 'ACCESS_SETTINGS']
  },
  {
    role: 'ADMIN',
    module: 'NOTIFICATION',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'ADMIN',
    module: 'DOCUMENT',
    permissions: ['UPLOAD', 'READ', 'DOWNLOAD']
  },
  {
    role: 'ADMIN',
    module: 'REPORTS',
    permissions: ['READ', 'EXPORT', 'PRINT']
  },
  {
    role: 'ADMIN',
    module: 'DASHBOARD',
    permissions: ['READ']
  },
  {
    role: 'ADMIN',
    module: 'FEEDBACK',
    permissions: ['READ']
  },
  {
    role: 'ADMIN',
    module: 'SUPPORT',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'ADMIN',
    module: 'ANALYTICS',
    permissions: ['READ']
  },
  {
    role: 'ADMIN',
    module: 'ALERTS',
    permissions: ['READ']
  },
  {
    role: 'ADMIN',
    module: 'SECURITY',
    permissions: ['READ']
  },
  {
    role: 'ADMIN',
    module: 'FINANCIAL',
    permissions: ['READ']
  },
  {
    role: 'ADMIN',
    module: 'AUDIT',
    permissions: ['READ']
  },

  // STAFF
  {
    role: 'STAFF',
    module: 'SALES',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'STAFF',
    module: 'SALES_RETURN',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'STAFF',
    module: 'INVENTORY',
    permissions: ['READ']
  },
  {
    role: 'STAFF',
    module: 'BILLING',
    permissions: ['READ', 'PRINT']
  },
  {
    role: 'STAFF',
    module: 'DOCUMENT',
    permissions: ['UPLOAD', 'READ']
  },
  {
    role: 'STAFF',
    module: 'CUSTOMER',
    permissions: ['CREATE', 'READ']
  },
  {
    role: 'STAFF',
    module: 'DASHBOARD',
    permissions: ['READ']
  },
  // Add missing modules for STAFF - Very limited access (mostly READ only)
  {
    role: 'STAFF',
    module: 'PRODUCT',
    permissions: ['READ']
  },
  {
    role: 'STAFF',
    module: 'CATEGORY',
    permissions: ['READ']
  },
  {
    role: 'STAFF',
    module: 'SUPPLIER',
    permissions: ['READ']
  },
  {
    role: 'STAFF',
    module: 'NOTIFICATION',
    permissions: ['READ']
  },
  {
    role: 'STAFF',
    module: 'REPORTS',
    permissions: ['READ']
  },

  // DISTRIBUTOR
  {
    role: 'DISTRIBUTOR',
    module: 'PURCHASE',
    permissions: ['READ', 'APPROVE', 'REJECT']
  },
  {
    role: 'DISTRIBUTOR',
    module: 'PURCHASE_RETURN',
    permissions: ['READ', 'APPROVE']
  },
  {
    role: 'DISTRIBUTOR',
    module: 'DASHBOARD',
    permissions: ['READ']
  }
]

export function hasPermission(
  userRole: Role,
  module: Module,
  permission: Permission
): boolean {
  const rolePermissions = ROLE_PERMISSIONS.filter(
    (rp) => rp.role === userRole && rp.module === module
  )

  return rolePermissions.some((rp) => rp.permissions.includes(permission))
}

export function getUserModules(userRole: Role): Module[] {
  return ROLE_PERMISSIONS
    .filter((rp) => rp.role === userRole)
    .map((rp) => rp.module)
}

export function getModulePermissions(userRole: Role, module: Module): Permission[] {
  const rolePermission = ROLE_PERMISSIONS.find(
    (rp) => rp.role === userRole && rp.module === module
  )

  return rolePermission?.permissions || []
}