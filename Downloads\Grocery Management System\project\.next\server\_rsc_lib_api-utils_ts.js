"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_lib_api-utils_ts";
exports.ids = ["_rsc_lib_api-utils_ts"];
exports.modules = {

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request, context)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user, context);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXBpLXV0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ2Y7QUFDUDtBQUVqQyxnQ0FBZ0M7QUFDekIsU0FBU0csZUFBZUMsSUFBUyxFQUFFQyxTQUFpQixHQUFHO0lBQzVELE9BQU9MLGtGQUFZQSxDQUFDTSxJQUFJLENBQUNGLE1BQU07UUFBRUM7SUFBTztBQUMxQztBQUVBLHlCQUF5QjtBQUNsQixTQUFTRSxvQkFBb0JDLE9BQWUsRUFBRUgsU0FBaUIsR0FBRztJQUN2RSxPQUFPTCxrRkFBWUEsQ0FBQ00sSUFBSSxDQUFDO1FBQUVHLE9BQU9EO0lBQVEsR0FBRztRQUFFSDtJQUFPO0FBQ3hEO0FBRUEsMkJBQTJCO0FBQ3BCLFNBQVNLLHNCQUFzQk4sSUFBUyxFQUFFSSxPQUFnQjtJQUMvRCxPQUFPUixrRkFBWUEsQ0FBQ00sSUFBSSxDQUFDO1FBQ3ZCSyxTQUFTO1FBQ1RQO1FBQ0FJO0lBQ0Y7QUFDRjtBQUVBLDZCQUE2QjtBQUN0QixTQUFTSSx3QkFDZFIsSUFBVyxFQUNYUyxJQUFZLEVBQ1pDLEtBQWEsRUFDYkMsS0FBYTtJQUViLE9BQU9mLGtGQUFZQSxDQUFDTSxJQUFJLENBQUM7UUFDdkJGO1FBQ0FZLFlBQVk7WUFDVkg7WUFDQUM7WUFDQUM7WUFDQUUsWUFBWUMsS0FBS0MsSUFBSSxDQUFDSixRQUFRRDtZQUM5Qk0sU0FBU1AsT0FBT0MsUUFBUUM7WUFDeEJNLFNBQVNSLE9BQU87UUFDbEI7SUFDRjtBQUNGO0FBRUEsdUNBQXVDO0FBQ2hDLFNBQVNTLGVBQ2RDLE1BQWMsRUFDZEMsVUFBa0IsRUFDbEJDLE9BQWtGO0lBRWxGLE9BQU8sT0FBT0MsU0FBc0JDO1FBQ2xDLElBQUk7WUFDRixNQUFNQyxrQkFBa0IsTUFBTTNCLHNEQUFlQSxDQUFDeUIsU0FBU0gsUUFBUUM7WUFFL0QsSUFBSUksZ0JBQWdCbkIsS0FBSyxFQUFFO2dCQUN6QixPQUFPRixvQkFBb0JxQixnQkFBZ0JuQixLQUFLLEVBQUVtQixnQkFBZ0J2QixNQUFNO1lBQzFFO1lBRUEsT0FBTyxNQUFNb0IsUUFBUUMsU0FBU0UsZ0JBQWdCQyxJQUFJLEVBQUVGO1FBQ3RELEVBQUUsT0FBT2xCLE9BQU87WUFDZHFCLFFBQVFyQixLQUFLLENBQUMsQ0FBQyxhQUFhLEVBQUVjLE9BQU8sQ0FBQyxDQUFDLEVBQUVkO1lBQ3pDLE9BQU9GLG9CQUFvQix5QkFBeUI7UUFDdEQ7SUFDRjtBQUNGO0FBRUEsbUJBQW1CO0FBQ1osZUFBZXdCLGVBQ3BCQyxNQUFjLEVBQ2RULE1BQWMsRUFDZFUsT0FBZSxFQUNmQyxNQUFjLEVBQ2RDLE9BQWU7SUFFZixJQUFJO1FBQ0YsTUFBTWpDLDJDQUFNQSxDQUFDa0MsUUFBUSxDQUFDQyxNQUFNLENBQUM7WUFDM0JqQyxNQUFNO2dCQUNKNEI7Z0JBQ0FUO2dCQUNBVTtnQkFDQUM7Z0JBQ0FDO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBTzFCLE9BQU87UUFDZHFCLFFBQVFyQixLQUFLLENBQUMsK0JBQStCQTtJQUMvQztBQUNGO0FBRUEsb0JBQW9CO0FBQ2IsU0FBUzZCLG9CQUFvQlosT0FBb0I7SUFDdEQsTUFBTWEsTUFBTSxJQUFJQyxJQUFJZCxRQUFRYSxHQUFHO0lBQy9CLE1BQU0xQixPQUFPNEIsU0FBU0YsSUFBSUcsWUFBWSxDQUFDQyxHQUFHLENBQUMsV0FBVztJQUN0RCxNQUFNN0IsUUFBUTJCLFNBQVNGLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDLFlBQVk7SUFDeEQsTUFBTUMsU0FBU0wsSUFBSUcsWUFBWSxDQUFDQyxHQUFHLENBQUMsYUFBYTtJQUNqRCxNQUFNRSxTQUFTTixJQUFJRyxZQUFZLENBQUNDLEdBQUcsQ0FBQyxhQUFhO0lBQ2pELE1BQU1HLFlBQVlQLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDLGdCQUFnQjtJQUV2RCxPQUFPO1FBQ0w5QixNQUFNSyxLQUFLNkIsR0FBRyxDQUFDLEdBQUdsQztRQUNsQkMsT0FBT0ksS0FBSzhCLEdBQUcsQ0FBQyxLQUFLOUIsS0FBSzZCLEdBQUcsQ0FBQyxHQUFHakM7UUFDakNtQyxNQUFNLENBQUMvQixLQUFLNkIsR0FBRyxDQUFDLEdBQUdsQyxRQUFRLEtBQUtLLEtBQUs4QixHQUFHLENBQUMsS0FBSzlCLEtBQUs2QixHQUFHLENBQUMsR0FBR2pDO1FBQzFEOEI7UUFDQUM7UUFDQUMsV0FBV0EsY0FBYyxRQUFRLFFBQVE7SUFDM0M7QUFDRjtBQUVBLHVCQUF1QjtBQUNoQixTQUFTSSxrQkFBa0JOLE1BQWMsRUFBRU8sTUFBZ0I7SUFDaEUsSUFBSSxDQUFDUCxRQUFRLE9BQU8sQ0FBQztJQUVyQixPQUFPO1FBQ0xRLElBQUlELE9BQU9FLEdBQUcsQ0FBQ0MsQ0FBQUEsUUFBVTtnQkFDdkIsQ0FBQ0EsTUFBTSxFQUFFO29CQUNQQyxVQUFVWDtvQkFDVlksTUFBTTtnQkFDUjtZQUNGO0lBQ0Y7QUFDRjtBQUVBLDJCQUEyQjtBQUNwQixTQUFTQyxxQkFDZEMsU0FBa0IsRUFDbEJDLE9BQWdCLEVBQ2hCTCxRQUFnQixXQUFXO0lBRTNCLE1BQU1NLFNBQWMsQ0FBQztJQUVyQixJQUFJRixXQUFXO1FBQ2JFLE1BQU0sQ0FBQ04sTUFBTSxHQUFHO1lBQUUsR0FBR00sTUFBTSxDQUFDTixNQUFNO1lBQUVPLEtBQUssSUFBSUMsS0FBS0o7UUFBVztJQUMvRDtJQUVBLElBQUlDLFNBQVM7UUFDWCxNQUFNSSxNQUFNLElBQUlELEtBQUtIO1FBQ3JCSSxJQUFJQyxRQUFRLENBQUMsSUFBSSxJQUFJLElBQUk7UUFDekJKLE1BQU0sQ0FBQ04sTUFBTSxHQUFHO1lBQUUsR0FBR00sTUFBTSxDQUFDTixNQUFNO1lBQUVXLEtBQUtGO1FBQUk7SUFDL0M7SUFFQSxPQUFPRyxPQUFPQyxJQUFJLENBQUNQLFFBQVFRLE1BQU0sR0FBRyxJQUFJUixTQUFTLENBQUM7QUFDcEQ7QUFFQSxvQkFBb0I7QUFDYixTQUFTUyx1QkFBdUJqRSxJQUFTLEVBQUUrQyxNQUFnQjtJQUNoRSxNQUFNbUIsVUFBVW5CLE9BQU9TLE1BQU0sQ0FBQ04sQ0FBQUEsUUFBUyxDQUFDbEQsSUFBSSxDQUFDa0QsTUFBTTtJQUVuRCxJQUFJZ0IsUUFBUUYsTUFBTSxHQUFHLEdBQUc7UUFDdEIsTUFBTSxJQUFJRyxNQUFNLENBQUMseUJBQXlCLEVBQUVELFFBQVFFLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDbEU7QUFDRjtBQUVBLDBCQUEwQjtBQUNuQixTQUFTQyxvQkFBb0I1QyxJQUFTO0lBQzNDLElBQUksQ0FBQ0EsS0FBS00sT0FBTyxFQUFFO1FBQ2pCLE1BQU0sSUFBSW9DLE1BQU07SUFDbEI7SUFDQSxPQUFPMUMsS0FBS00sT0FBTztBQUNyQiIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvYXBpLXV0aWxzLnRzPzdkYTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgY2hlY2tQZXJtaXNzaW9uIH0gZnJvbSAnLi9hdXRoJ1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnXG5cbi8vIFN0YW5kYXJkIEFQSSByZXNwb25zZSB3cmFwcGVyXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlUmVzcG9uc2UoZGF0YTogYW55LCBzdGF0dXM6IG51bWJlciA9IDIwMCkge1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZGF0YSwgeyBzdGF0dXMgfSlcbn1cblxuLy8gRXJyb3IgcmVzcG9uc2Ugd3JhcHBlclxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUVycm9yUmVzcG9uc2UobWVzc2FnZTogc3RyaW5nLCBzdGF0dXM6IG51bWJlciA9IDQwMCkge1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogbWVzc2FnZSB9LCB7IHN0YXR1cyB9KVxufVxuXG4vLyBTdWNjZXNzIHJlc3BvbnNlIHdyYXBwZXJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTdWNjZXNzUmVzcG9uc2UoZGF0YTogYW55LCBtZXNzYWdlPzogc3RyaW5nKSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICBkYXRhLFxuICAgIG1lc3NhZ2VcbiAgfSlcbn1cblxuLy8gUGFnaW5hdGVkIHJlc3BvbnNlIHdyYXBwZXJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVQYWdpbmF0ZWRSZXNwb25zZShcbiAgZGF0YTogYW55W10sXG4gIHBhZ2U6IG51bWJlcixcbiAgbGltaXQ6IG51bWJlcixcbiAgdG90YWw6IG51bWJlclxuKSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgZGF0YSxcbiAgICBwYWdpbmF0aW9uOiB7XG4gICAgICBwYWdlLFxuICAgICAgbGltaXQsXG4gICAgICB0b3RhbCxcbiAgICAgIHRvdGFsUGFnZXM6IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KSxcbiAgICAgIGhhc05leHQ6IHBhZ2UgKiBsaW1pdCA8IHRvdGFsLFxuICAgICAgaGFzUHJldjogcGFnZSA+IDFcbiAgICB9XG4gIH0pXG59XG5cbi8vIFBlcm1pc3Npb24tYmFzZWQgQVBJIGhhbmRsZXIgd3JhcHBlclxuZXhwb3J0IGZ1bmN0aW9uIHdpdGhQZXJtaXNzaW9uKFxuICBtb2R1bGU6IHN0cmluZyxcbiAgcGVybWlzc2lvbjogc3RyaW5nLFxuICBoYW5kbGVyOiAocmVxdWVzdDogTmV4dFJlcXVlc3QsIHVzZXI6IGFueSwgY29udGV4dD86IGFueSkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+XG4pIHtcbiAgcmV0dXJuIGFzeW5jIChyZXF1ZXN0OiBOZXh0UmVxdWVzdCwgY29udGV4dD86IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwZXJtaXNzaW9uQ2hlY2sgPSBhd2FpdCBjaGVja1Blcm1pc3Npb24ocmVxdWVzdCwgbW9kdWxlLCBwZXJtaXNzaW9uKVxuXG4gICAgICBpZiAocGVybWlzc2lvbkNoZWNrLmVycm9yKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVFcnJvclJlc3BvbnNlKHBlcm1pc3Npb25DaGVjay5lcnJvciwgcGVybWlzc2lvbkNoZWNrLnN0YXR1cylcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGF3YWl0IGhhbmRsZXIocmVxdWVzdCwgcGVybWlzc2lvbkNoZWNrLnVzZXIsIGNvbnRleHQpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEFQSSBFcnJvciBpbiAke21vZHVsZX06YCwgZXJyb3IpXG4gICAgICByZXR1cm4gY3JlYXRlRXJyb3JSZXNwb25zZSgnSW50ZXJuYWwgc2VydmVyIGVycm9yJywgNTAwKVxuICAgIH1cbiAgfVxufVxuXG4vLyBBdWRpdCBsb2cgaGVscGVyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlQXVkaXRMb2coXG4gIGFjdGlvbjogc3RyaW5nLFxuICBtb2R1bGU6IHN0cmluZyxcbiAgZGV0YWlsczogc3RyaW5nLFxuICB1c2VySWQ6IHN0cmluZyxcbiAgc3RvcmVJZDogc3RyaW5nXG4pIHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBwcmlzbWEuYXVkaXRMb2cuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgYWN0aW9uLFxuICAgICAgICBtb2R1bGUsXG4gICAgICAgIGRldGFpbHMsXG4gICAgICAgIHVzZXJJZCxcbiAgICAgICAgc3RvcmVJZFxuICAgICAgfVxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBhdWRpdCBsb2c6JywgZXJyb3IpXG4gIH1cbn1cblxuLy8gUGFnaW5hdGlvbiBoZWxwZXJcbmV4cG9ydCBmdW5jdGlvbiBnZXRQYWdpbmF0aW9uUGFyYW1zKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIGNvbnN0IHVybCA9IG5ldyBVUkwocmVxdWVzdC51cmwpXG4gIGNvbnN0IHBhZ2UgPSBwYXJzZUludCh1cmwuc2VhcmNoUGFyYW1zLmdldCgncGFnZScpIHx8ICcxJylcbiAgY29uc3QgbGltaXQgPSBwYXJzZUludCh1cmwuc2VhcmNoUGFyYW1zLmdldCgnbGltaXQnKSB8fCAnMTAnKVxuICBjb25zdCBzZWFyY2ggPSB1cmwuc2VhcmNoUGFyYW1zLmdldCgnc2VhcmNoJykgfHwgJydcbiAgY29uc3Qgc29ydEJ5ID0gdXJsLnNlYXJjaFBhcmFtcy5nZXQoJ3NvcnRCeScpIHx8ICdjcmVhdGVkQXQnXG4gIGNvbnN0IHNvcnRPcmRlciA9IHVybC5zZWFyY2hQYXJhbXMuZ2V0KCdzb3J0T3JkZXInKSB8fCAnZGVzYydcblxuICByZXR1cm4ge1xuICAgIHBhZ2U6IE1hdGgubWF4KDEsIHBhZ2UpLFxuICAgIGxpbWl0OiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDEsIGxpbWl0KSksXG4gICAgc2tpcDogKE1hdGgubWF4KDEsIHBhZ2UpIC0gMSkgKiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDEsIGxpbWl0KSksXG4gICAgc2VhcmNoLFxuICAgIHNvcnRCeSxcbiAgICBzb3J0T3JkZXI6IHNvcnRPcmRlciA9PT0gJ2FzYycgPyAnYXNjJyA6ICdkZXNjJ1xuICB9XG59XG5cbi8vIFNlYXJjaCBmaWx0ZXIgaGVscGVyXG5leHBvcnQgZnVuY3Rpb24gYnVpbGRTZWFyY2hGaWx0ZXIoc2VhcmNoOiBzdHJpbmcsIGZpZWxkczogc3RyaW5nW10pIHtcbiAgaWYgKCFzZWFyY2gpIHJldHVybiB7fVxuXG4gIHJldHVybiB7XG4gICAgT1I6IGZpZWxkcy5tYXAoZmllbGQgPT4gKHtcbiAgICAgIFtmaWVsZF06IHtcbiAgICAgICAgY29udGFpbnM6IHNlYXJjaCxcbiAgICAgICAgbW9kZTogJ2luc2Vuc2l0aXZlJ1xuICAgICAgfVxuICAgIH0pKVxuICB9XG59XG5cbi8vIERhdGUgcmFuZ2UgZmlsdGVyIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIGJ1aWxkRGF0ZVJhbmdlRmlsdGVyKFxuICBzdGFydERhdGU/OiBzdHJpbmcsXG4gIGVuZERhdGU/OiBzdHJpbmcsXG4gIGZpZWxkOiBzdHJpbmcgPSAnY3JlYXRlZEF0J1xuKSB7XG4gIGNvbnN0IGZpbHRlcjogYW55ID0ge31cblxuICBpZiAoc3RhcnREYXRlKSB7XG4gICAgZmlsdGVyW2ZpZWxkXSA9IHsgLi4uZmlsdGVyW2ZpZWxkXSwgZ3RlOiBuZXcgRGF0ZShzdGFydERhdGUpIH1cbiAgfVxuXG4gIGlmIChlbmREYXRlKSB7XG4gICAgY29uc3QgZW5kID0gbmV3IERhdGUoZW5kRGF0ZSlcbiAgICBlbmQuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KVxuICAgIGZpbHRlcltmaWVsZF0gPSB7IC4uLmZpbHRlcltmaWVsZF0sIGx0ZTogZW5kIH1cbiAgfVxuXG4gIHJldHVybiBPYmplY3Qua2V5cyhmaWx0ZXIpLmxlbmd0aCA+IDAgPyBmaWx0ZXIgOiB7fVxufVxuXG4vLyBWYWxpZGF0aW9uIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlUmVxdWlyZWRGaWVsZHMoZGF0YTogYW55LCBmaWVsZHM6IHN0cmluZ1tdKSB7XG4gIGNvbnN0IG1pc3NpbmcgPSBmaWVsZHMuZmlsdGVyKGZpZWxkID0+ICFkYXRhW2ZpZWxkXSlcblxuICBpZiAobWlzc2luZy5sZW5ndGggPiAwKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBNaXNzaW5nIHJlcXVpcmVkIGZpZWxkczogJHttaXNzaW5nLmpvaW4oJywgJyl9YClcbiAgfVxufVxuXG4vLyBTdG9yZSB2YWxpZGF0aW9uIGhlbHBlclxuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlU3RvcmVBY2Nlc3ModXNlcjogYW55KSB7XG4gIGlmICghdXNlci5zdG9yZUlkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhc3NvY2lhdGVkIHdpdGggYW55IHN0b3JlJylcbiAgfVxuICByZXR1cm4gdXNlci5zdG9yZUlkXG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY2hlY2tQZXJtaXNzaW9uIiwicHJpc21hIiwiY3JlYXRlUmVzcG9uc2UiLCJkYXRhIiwic3RhdHVzIiwianNvbiIsImNyZWF0ZUVycm9yUmVzcG9uc2UiLCJtZXNzYWdlIiwiZXJyb3IiLCJjcmVhdGVTdWNjZXNzUmVzcG9uc2UiLCJzdWNjZXNzIiwiY3JlYXRlUGFnaW5hdGVkUmVzcG9uc2UiLCJwYWdlIiwibGltaXQiLCJ0b3RhbCIsInBhZ2luYXRpb24iLCJ0b3RhbFBhZ2VzIiwiTWF0aCIsImNlaWwiLCJoYXNOZXh0IiwiaGFzUHJldiIsIndpdGhQZXJtaXNzaW9uIiwibW9kdWxlIiwicGVybWlzc2lvbiIsImhhbmRsZXIiLCJyZXF1ZXN0IiwiY29udGV4dCIsInBlcm1pc3Npb25DaGVjayIsInVzZXIiLCJjb25zb2xlIiwiY3JlYXRlQXVkaXRMb2ciLCJhY3Rpb24iLCJkZXRhaWxzIiwidXNlcklkIiwic3RvcmVJZCIsImF1ZGl0TG9nIiwiY3JlYXRlIiwiZ2V0UGFnaW5hdGlvblBhcmFtcyIsInVybCIsIlVSTCIsInBhcnNlSW50Iiwic2VhcmNoUGFyYW1zIiwiZ2V0Iiwic2VhcmNoIiwic29ydEJ5Iiwic29ydE9yZGVyIiwibWF4IiwibWluIiwic2tpcCIsImJ1aWxkU2VhcmNoRmlsdGVyIiwiZmllbGRzIiwiT1IiLCJtYXAiLCJmaWVsZCIsImNvbnRhaW5zIiwibW9kZSIsImJ1aWxkRGF0ZVJhbmdlRmlsdGVyIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsImZpbHRlciIsImd0ZSIsIkRhdGUiLCJlbmQiLCJzZXRIb3VycyIsImx0ZSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJ2YWxpZGF0ZVJlcXVpcmVkRmllbGRzIiwibWlzc2luZyIsIkVycm9yIiwiam9pbiIsInZhbGlkYXRlU3RvcmVBY2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ })

};
;