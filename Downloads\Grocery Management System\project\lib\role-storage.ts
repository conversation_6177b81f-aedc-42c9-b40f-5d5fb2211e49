// Temporary in-memory storage for custom roles
// In production, this would be replaced with database operations

export interface CustomRole {
  id: string
  name: string
  description: string
  isCustom: boolean
  modules: string[]
  permissions: Record<string, string[]>
  moduleCount: number
  permissionCount: number
  createdAt: string
  updatedAt?: string
}

// Shared storage for custom roles
let customRoles: CustomRole[] = []

export const roleStorage = {
  // Get all custom roles
  getAll: (): CustomRole[] => {
    return [...customRoles]
  },

  // Get a custom role by ID
  getById: (id: string): CustomRole | undefined => {
    return customRoles.find(role => role.id === id)
  },

  // Add a new custom role
  add: (role: CustomRole): void => {
    customRoles.push(role)
  },

  // Update a custom role
  update: (id: string, updateData: Partial<CustomRole>): CustomRole | null => {
    const index = customRoles.findIndex(role => role.id === id)
    if (index === -1) return null

    customRoles[index] = { ...customRoles[index], ...updateData }
    return customRoles[index]
  },

  // Delete a custom role
  delete: (id: string): boolean => {
    const initialLength = customRoles.length
    customRoles = customRoles.filter(role => role.id !== id)
    return customRoles.length < initialLength
  },

  // Clear all custom roles (for testing)
  clear: (): void => {
    customRoles = []
  }
}

// Predefined roles data
export const predefinedRoles = [
  {
    id: 'founder',
    name: 'FOUNDER',
    description: 'System founder with complete access',
    isCustom: false,
    moduleCount: 27,
    permissionCount: 150,
    createdAt: new Date().toISOString()
  },
  {
    id: 'super_admin',
    name: 'SUPER_ADMIN',
    description: 'Super administrator with extensive access',
    isCustom: false,
    moduleCount: 25,
    permissionCount: 120,
    createdAt: new Date().toISOString()
  },
  {
    id: 'admin',
    name: 'ADMIN',
    description: 'Administrator with operational access',
    isCustom: false,
    moduleCount: 20,
    permissionCount: 80,
    createdAt: new Date().toISOString()
  },
  {
    id: 'staff',
    name: 'STAFF',
    description: 'Staff member with limited access',
    isCustom: false,
    moduleCount: 10,
    permissionCount: 30,
    createdAt: new Date().toISOString()
  },
  {
    id: 'distributor',
    name: 'DISTRIBUTOR',
    description: 'Distributor with specific access',
    isCustom: false,
    moduleCount: 8,
    permissionCount: 25,
    createdAt: new Date().toISOString()
  }
]
