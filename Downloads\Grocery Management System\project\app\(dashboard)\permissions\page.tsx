'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Shield,
  Users,
  Settings,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Save,
  RefreshCw
} from 'lucide-react'
import { toast } from 'sonner'
import { ROLE_PERMISSIONS, hasPermission, getUserModules, getModulePermissions } from '@/lib/permissions'
import { Role, Module, Permission } from '@/lib/types'

interface ModuleInfo {
  name: string
  description: string
  whoUses: string[]
  useCases: string[]
}

const MODULE_INFO: Record<Module, ModuleInfo> = {
  USER: {
    name: 'User Management',
    description: 'All system users (founder, super admin, admin, staff, distributor)',
    whoUses: ['Founder', 'Super Admin', 'Admin'],
    useCases: ['Create staff/distributor accounts', 'Edit or suspend users', 'Assign users to stores or roles']
  },
  ROLE: {
    name: 'Role Management',
    description: 'Role & Permission management (RBAC)',
    whoUses: ['Founder'],
    useCases: ['Define what each role can access', 'Customize permissions for modules']
  },
  STORE: {
    name: 'Store Management',
    description: 'Create and manage physical store branches',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Founder creates stores', 'Super Admin updates store status/details']
  },
  PRODUCT: {
    name: 'Product Management',
    description: 'Master list of all sellable products',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Add/edit products', 'Upload products in bulk via Excel', 'Export product catalog']
  },
  CATEGORY: {
    name: 'Category Management',
    description: 'Product categories like Grocery, Beverages, etc.',
    whoUses: ['Admin'],
    useCases: ['Create "Snacks", "Fruits", "Bakery" categories', 'Update names or delete unused categories']
  },
  INVENTORY: {
    name: 'Inventory Management',
    description: 'Tracks stock in/out levels per store',
    whoUses: ['Admin', 'Staff'],
    useCases: ['View stock quantity', 'Update quantity after stock count', 'Export stock levels']
  },
  PURCHASE: {
    name: 'Purchase Management',
    description: 'Orders made to suppliers/distributors',
    whoUses: ['Admin', 'Distributor'],
    useCases: ['Create a PO (purchase order)', 'Distributor approves and dispatches']
  },
  PURCHASE_RETURN: {
    name: 'Purchase Return',
    description: 'Returned items to suppliers/distributors',
    whoUses: ['Admin', 'Distributor'],
    useCases: ['Return expired or damaged products']
  },
  SALES: {
    name: 'Sales Management',
    description: 'POS billing & sales transactions',
    whoUses: ['Staff', 'Admin'],
    useCases: ['Staff bills a walk-in customer', 'Admin prints daily sales report']
  },
  SALES_RETURN: {
    name: 'Sales Return',
    description: 'Items returned by customers',
    whoUses: ['Staff', 'Admin'],
    useCases: ['Process a customer return', 'Refund or adjust bill']
  },
  CUSTOMER: {
    name: 'Customer Management',
    description: 'Customer master data (optional)',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Add loyal customers', 'Attach bills to registered users']
  },
  DISTRIBUTOR: {
    name: 'Distributor Management',
    description: 'Distributor/supplier assigned to a store',
    whoUses: ['Admin'],
    useCases: ['Add or update a vendor', 'View supplier history']
  },
  EXPENSE: {
    name: 'Expense Management',
    description: 'Daily/monthly expense tracking',
    whoUses: ['Admin'],
    useCases: ['Track store electricity, rent, salary, etc.', 'Generate monthly cost reports']
  },
  SUPPLIER: {
    name: 'Supplier Management',
    description: 'Supplier master (can be same as Distributor)',
    whoUses: ['Admin'],
    useCases: ['View supplier-wise product rates', 'Update supplier contact details']
  },
  PAYMENT: {
    name: 'Payment Management',
    description: 'Tracks in/out cash flows',
    whoUses: ['Admin'],
    useCases: ['Add vendor payments', 'Track payment dues']
  },
  B2B: {
    name: 'B2B Management',
    description: 'Manage wholesale (business-to-business) clients',
    whoUses: ['Admin'],
    useCases: ['Create a bulk order from a nearby Kirana store', 'Approve credit-based sales']
  },
  BILLING: {
    name: 'Billing Management',
    description: 'POS Invoice management',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Print bills', 'View previous transactions']
  },
  TAX: {
    name: 'Tax Management',
    description: 'GST/VAT configuration',
    whoUses: ['Admin', 'Super Admin'],
    useCases: ['Apply tax slabs to products', 'Update tax rules when regulations change']
  },
  NOTIFICATION: {
    name: 'Notification Management',
    description: 'Alert/reminder system',
    whoUses: ['Admin'],
    useCases: ['Low-stock alert', 'Expiry alert for products']
  },
  DOCUMENT: {
    name: 'Document Management',
    description: 'Upload and manage store/supplier documents',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Upload invoices, supplier GST files', 'Download purchase receipts']
  },
  SETTINGS: {
    name: 'Settings Management',
    description: 'Store and system-level configurations',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Enable/disable loyalty system', 'Customize invoice templates']
  },
  REPORTS: {
    name: 'Reports Management',
    description: 'Financial, inventory, sales reports',
    whoUses: ['Admin', 'Super Admin'],
    useCases: ['Generate monthly sales summary', 'Export P&L report']
  },
  DASHBOARD: {
    name: 'Dashboard',
    description: 'Real-time statistics and charts',
    whoUses: ['All roles (different visibility)'],
    useCases: ['Track today\'s sales', 'View low stock notifications']
  },
  AUDIT_LOG: {
    name: 'Audit Log',
    description: 'Logs of who did what',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Detect unauthorized changes', 'Track store staff activities']
  },
  SUBSCRIPTION: {
    name: 'Subscription Management',
    description: 'SaaS billing plan (per store)',
    whoUses: ['Founder'],
    useCases: ['Assign monthly/annual subscription plans', 'Enable/disable features based on plan']
  },
  FEEDBACK: {
    name: 'Feedback Management',
    description: 'Collect and view feedback from customers',
    whoUses: ['Admin'],
    useCases: ['Analyze customer satisfaction', 'Improve store experience']
  },
  SUPPORT: {
    name: 'Support Management',
    description: 'Store support/ticket system',
    whoUses: ['Admin', 'Super Admin'],
    useCases: ['Admin raises issues to support', 'View ticket resolutions']
  },
  ANALYTICS: {
    name: 'Analytics Management',
    description: 'Business intelligence and data analytics',
    whoUses: ['Founder', 'Super Admin', 'Admin'],
    useCases: ['View sales trends', 'Customer behavior analysis', 'Performance metrics']
  },
  ALERTS: {
    name: 'Alerts Management',
    description: 'System alerts and monitoring',
    whoUses: ['Founder', 'Super Admin', 'Admin'],
    useCases: ['Low stock alerts', 'System health monitoring', 'Security alerts']
  },
  SECURITY: {
    name: 'Security Management',
    description: 'Security monitoring and access control',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Monitor login attempts', 'Security breach detection', 'Access logs']
  },
  FINANCIAL: {
    name: 'Financial Management',
    description: 'Financial reports and accounting',
    whoUses: ['Founder', 'Super Admin', 'Admin'],
    useCases: ['P&L reports', 'Cash flow analysis', 'Financial statements']
  },
  AUDIT: {
    name: 'Audit Management',
    description: 'System audit trails and activity logs',
    whoUses: ['Founder', 'Super Admin', 'Admin'],
    useCases: ['Track user activities', 'Compliance reporting', 'Change history']
  }
}

const ROLE_COLORS: Record<Role, string> = {
  FOUNDER: 'bg-purple-100 text-purple-800 border-purple-200',
  SUPER_ADMIN: 'bg-blue-100 text-blue-800 border-blue-200',
  ADMIN: 'bg-green-100 text-green-800 border-green-200',
  STAFF: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  DISTRIBUTOR: 'bg-orange-100 text-orange-800 border-orange-200'
}

export default function PermissionsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<Role | 'ALL'>('ALL')
  const [selectedModule, setSelectedModule] = useState<Module | 'ALL'>('ALL')
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false)
  const [newRoleName, setNewRoleName] = useState('')
  const [newRoleDescription, setNewRoleDescription] = useState('')
  const [selectedModules, setSelectedModules] = useState<Module[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<Record<Module, Permission[]>>({})
  const [roles, setRoles] = useState<any[]>([])
  const [permissionMatrix, setPermissionMatrix] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isViewRoleDialogOpen, setIsViewRoleDialogOpen] = useState(false)
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false)
  const [selectedRoleForView, setSelectedRoleForView] = useState<any>(null)
  const [isPermissionEditorOpen, setIsPermissionEditorOpen] = useState(false)
  const [editingPermissions, setEditingPermissions] = useState<Record<string, any>>({})

  const defaultRoles: Role[] = ['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']
  const modules: Module[] = Object.keys(MODULE_INFO) as Module[]
  const allPermissions: Permission[] = ['CREATE', 'READ', 'UPDATE', 'DELETE', 'EXPORT', 'IMPORT', 'ASSIGN', 'APPROVE', 'REJECT', 'MANAGE', 'ACCESS_SETTINGS', 'UPLOAD', 'DOWNLOAD', 'PRINT', 'ARCHIVE', 'RESTORE']

  // Fetch roles and permissions data
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/roles')
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setRoles(result.data)
        } else {
          console.error('Invalid API response:', result)
          toast.error('Invalid response from server')
        }
      } else {
        const error = await response.json()
        console.error('API error:', error)
        toast.error(error.message || 'Failed to fetch roles')
      }
    } catch (error) {
      console.error('Error fetching roles:', error)
      toast.error('Failed to fetch roles')
    }
  }

  const fetchPermissionMatrix = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedRole !== 'ALL') params.append('role', selectedRole)
      if (selectedModule !== 'ALL') params.append('module', selectedModule)

      const response = await fetch(`/api/roles/permissions?${params}`)
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setPermissionMatrix(result.data)
        } else {
          console.error('Invalid permission matrix response:', result)
          toast.error('Invalid permission matrix response')
        }
      } else {
        const error = await response.json()
        console.error('Permission matrix API error:', error)
        toast.error(error.message || 'Failed to fetch permission matrix')
      }
    } catch (error) {
      console.error('Error fetching permission matrix:', error)
      toast.error('Failed to fetch permission matrix')
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await Promise.all([fetchRoles(), fetchPermissionMatrix()])
      setIsLoading(false)
    }
    loadData()
  }, [selectedRole, selectedModule])

  const handleModuleToggle = (module: Module) => {
    setSelectedModules(prev =>
      prev.includes(module)
        ? prev.filter(m => m !== module)
        : [...prev, module]
    )
  }

  const handlePermissionToggle = (module: Module, permission: Permission) => {
    setSelectedPermissions(prev => ({
      ...prev,
      [module]: prev[module]?.includes(permission)
        ? prev[module].filter(p => p !== permission)
        : [...(prev[module] || []), permission]
    }))
  }

  const handleCreateCustomRole = async () => {
    if (!newRoleName.trim()) {
      toast.error('Please enter a role name')
      return
    }

    if (selectedModules.length === 0) {
      toast.error('Please select at least one module')
      return
    }

    try {
      const customRoleData = {
        name: newRoleName.toUpperCase().replace(/\s+/g, '_'),
        description: newRoleDescription,
        modules: selectedModules,
        permissions: selectedPermissions
      }

      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customRoleData)
      })

      if (response.ok) {
        const result = await response.json()
        toast.success(`Custom role "${newRoleName}" created successfully!`)

        // Refresh roles list
        await fetchRoles()

        // Reset form
        setNewRoleName('')
        setNewRoleDescription('')
        setSelectedModules([])
        setSelectedPermissions({})
        setIsAddRoleDialogOpen(false)
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to create custom role')
      }
    } catch (error) {
      console.error('Error creating custom role:', error)
      toast.error('Failed to create custom role')
    }
  }

  // View role handler
  const handleViewRole = (role: any) => {
    setSelectedRoleForView(role)
    setIsViewRoleDialogOpen(true)
  }

  // Edit role handler
  const handleEditRole = (role: any) => {
    setSelectedRoleForView(role)
    setIsEditRoleDialogOpen(true)
  }

  // Delete role handler
  const handleDeleteRole = async (roleId: string) => {
    try {
      const response = await fetch(`/api/roles?id=${roleId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Role deleted successfully')
        await fetchRoles()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to delete role')
      }
    } catch (error) {
      console.error('Error deleting role:', error)
      toast.error('Failed to delete role')
    }
  }

  // Permission editor handlers
  const handleOpenPermissionEditor = () => {
    setIsPermissionEditorOpen(true)
    // Initialize editing permissions with current matrix
    const editingPerms: Record<string, any> = {}
    permissionMatrix.forEach(item => {
      editingPerms[`${item.role}_${item.module}`] = {
        ...item,
        permissions: [...item.permissions]
      }
    })
    setEditingPermissions(editingPerms)
  }

  const handleSavePermissions = async () => {
    try {
      const updates = Object.values(editingPermissions).map((item: any) => ({
        role: item.role,
        module: item.module,
        permissions: item.permissions
      }))

      const response = await fetch('/api/roles/permissions/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates })
      })

      if (response.ok) {
        toast.success('Permissions updated successfully')
        await fetchPermissionMatrix()
        setIsPermissionEditorOpen(false)
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update permissions')
      }
    } catch (error) {
      console.error('Error updating permissions:', error)
      toast.error('Failed to update permissions')
    }
  }

  const filteredPermissions = permissionMatrix.filter(permission => {
    const matchesSearch = searchTerm === '' ||
      permission.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
      MODULE_INFO[permission.module]?.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = selectedRole === 'ALL' || permission.role === selectedRole
    const matchesModule = selectedModule === 'ALL' || permission.module === selectedModule

    return matchesSearch && matchesRole && matchesModule
  })

  const getPermissionColor = (permission: Permission) => {
    const colors: Record<Permission, string> = {
      CREATE: 'bg-green-100 text-green-800',
      READ: 'bg-blue-100 text-blue-800',
      UPDATE: 'bg-yellow-100 text-yellow-800',
      DELETE: 'bg-red-100 text-red-800',
      EXPORT: 'bg-purple-100 text-purple-800',
      IMPORT: 'bg-indigo-100 text-indigo-800',
      ASSIGN: 'bg-pink-100 text-pink-800',
      APPROVE: 'bg-emerald-100 text-emerald-800',
      REJECT: 'bg-rose-100 text-rose-800',
      MANAGE: 'bg-violet-100 text-violet-800',
      ACCESS_SETTINGS: 'bg-slate-100 text-slate-800',
      UPLOAD: 'bg-cyan-100 text-cyan-800',
      DOWNLOAD: 'bg-teal-100 text-teal-800',
      PRINT: 'bg-amber-100 text-amber-800',
      ARCHIVE: 'bg-gray-100 text-gray-800',
      RESTORE: 'bg-lime-100 text-lime-800'
    }
    return colors[permission] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Permission Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive Role-Based Access Control (RBAC) for 27 modules
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              setIsLoading(true)
              await Promise.all([fetchRoles(), fetchPermissionMatrix()])
              setIsLoading(false)
              toast.success('Data refreshed successfully')
            }}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <AlertDialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Custom Role
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <AlertDialogHeader>
                <AlertDialogTitle>Create Custom Role</AlertDialogTitle>
                <AlertDialogDescription>
                  Define a new role with specific module access and permissions.
                </AlertDialogDescription>
              </AlertDialogHeader>

              <div className="space-y-6 py-4">
                {/* Role Basic Info */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="role-name">Role Name *</Label>
                    <Input
                      id="role-name"
                      placeholder="e.g., MANAGER, SUPERVISOR, CASHIER"
                      value={newRoleName}
                      onChange={(e) => setNewRoleName(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="role-description">Description</Label>
                    <Input
                      id="role-description"
                      placeholder="Brief description of this role's responsibilities"
                      value={newRoleDescription}
                      onChange={(e) => setNewRoleDescription(e.target.value)}
                    />
                  </div>
                </div>

                <Separator />

                {/* Module Selection */}
                <div>
                  <Label className="text-base font-medium">Select Modules *</Label>
                  <p className="text-sm text-muted-foreground mb-4">
                    Choose which modules this role should have access to
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {modules.map(module => (
                      <Card key={module} className={`cursor-pointer transition-colors ${selectedModules.includes(module) ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                        }`}>
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <Checkbox
                              checked={selectedModules.includes(module)}
                              onCheckedChange={() => handleModuleToggle(module)}
                            />
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-sm">{MODULE_INFO[module].name}</h4>
                              <p className="text-xs text-muted-foreground mt-1">
                                {MODULE_INFO[module].description}
                              </p>

                              {selectedModules.includes(module) && (
                                <div className="mt-3 space-y-2">
                                  <Label className="text-xs font-medium">Permissions:</Label>
                                  <div className="grid grid-cols-2 gap-1">
                                    {allPermissions.slice(0, 8).map(permission => (
                                      <div key={permission} className="flex items-center space-x-1">
                                        <Checkbox
                                          checked={selectedPermissions[module]?.includes(permission) || false}
                                          onCheckedChange={() => handlePermissionToggle(module, permission)}
                                          className="h-3 w-3"
                                        />
                                        <span className="text-xs">{permission}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Summary */}
                {selectedModules.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <Label className="text-base font-medium">Role Summary</Label>
                      <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                        <p className="text-sm">
                          <strong>Role:</strong> {newRoleName || 'Unnamed Role'}
                        </p>
                        <p className="text-sm mt-1">
                          <strong>Modules:</strong> {selectedModules.length} selected
                        </p>
                        <p className="text-sm mt-1">
                          <strong>Total Permissions:</strong> {
                            Object.values(selectedPermissions).reduce((acc, perms) => acc + perms.length, 0)
                          }
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>

              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleCreateCustomRole}>
                  <Save className="h-4 w-4 mr-2" />
                  Create Role
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="roles">Role Matrix</TabsTrigger>
          <TabsTrigger value="modules">Module Details</TabsTrigger>
          <TabsTrigger value="permissions">Permission Editor</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{roles.length}</div>
                <p className="text-xs text-muted-foreground">
                  {roles.filter(r => r.isCustom).length} custom + {roles.filter(r => !r.isCustom).length} predefined
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Modules</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{modules.length}</div>
                <p className="text-xs text-muted-foreground">
                  Complete system coverage
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Permission Rules</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{ROLE_PERMISSIONS.length}</div>
                <p className="text-xs text-muted-foreground">
                  Active permission rules
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Permission Types</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">16</div>
                <p className="text-xs text-muted-foreground">
                  Different permission types
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Role Distribution</CardTitle>
              <CardDescription>
                Overview of permissions across different roles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  <div className="text-center py-4">Loading roles...</div>
                ) : (
                  roles.map(role => (
                    <div key={role.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge className={ROLE_COLORS[role.name as Role] || 'bg-gray-100 text-gray-800'}>
                          {role.name.replace('_', ' ')}
                        </Badge>
                        <div>
                          <p className="font-medium">{role.moduleCount} modules</p>
                          <p className="text-sm text-muted-foreground">{role.permissionCount} permissions</p>
                          {role.isCustom && (
                            <p className="text-xs text-blue-600">Custom Role</p>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleViewRole(role)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEditRole(role)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        {role.isCustom && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Custom Role</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete the "{role.name}" role? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteRole(role.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Role Permission Matrix</CardTitle>
              <CardDescription>
                Complete overview of what each role can access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="search">Search Modules</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="Search modules..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="role-filter">Filter by Role</Label>
                    <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as Role | 'ALL')}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ALL">All Roles</SelectItem>
                        {defaultRoles.map(role => (
                          <SelectItem key={role} value={role}>
                            {role.replace('_', ' ')}
                          </SelectItem>
                        ))}
                        {roles.filter(r => r.isCustom).map(role => (
                          <SelectItem key={role.id} value={role.name}>
                            {role.name.replace('_', ' ')} (Custom)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <ScrollArea className="h-[600px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Module</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Permissions</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-4">
                            Loading permission matrix...
                          </TableCell>
                        </TableRow>
                      ) : filteredPermissions.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-4">
                            No permissions found for the selected filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredPermissions.map((permission, index) => (
                          <TableRow key={permission.id || index}>
                            <TableCell>
                              <div>
                                <p className="font-medium">{MODULE_INFO[permission.module]?.name || permission.module}</p>
                                <p className="text-sm text-muted-foreground">
                                  {MODULE_INFO[permission.module]?.description || 'Custom module'}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={ROLE_COLORS[permission.role as Role] || 'bg-gray-100 text-gray-800'}>
                                {permission.role.replace('_', ' ')}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {permission.permissions.map((perm: Permission) => (
                                  <Badge
                                    key={perm}
                                    variant="outline"
                                    className={getPermissionColor(perm)}
                                  >
                                    {perm}
                                  </Badge>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // Open permission editor for this specific role-module combination
                                    setEditingPermissions({
                                      [`${permission.role}_${permission.module}`]: permission
                                    })
                                    setIsPermissionEditorOpen(true)
                                  }}
                                  disabled={permission.role === 'FOUNDER'}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                {permission.canEdit && (
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>Remove Permissions</AlertDialogTitle>
                                        <AlertDialogDescription>
                                          Are you sure you want to remove all permissions for {permission.role} on {permission.module}?
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                                          Remove
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {modules.map(module => {
              const moduleInfo = MODULE_INFO[module]
              const modulePermissions = ROLE_PERMISSIONS.filter(p => p.module === module)

              return (
                <Card key={module} className="h-full">
                  <CardHeader>
                    <CardTitle className="text-lg">{moduleInfo.name}</CardTitle>
                    <CardDescription>{moduleInfo.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Who Uses:</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {moduleInfo.whoUses.map(user => (
                          <Badge key={user} variant="secondary" className="text-xs">
                            {user}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Use Cases:</Label>
                      <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                        {moduleInfo.useCases.slice(0, 2).map((useCase, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-500 mt-1">•</span>
                            <span>{useCase}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Roles with Access:</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {modulePermissions.map(mp => (
                          <Badge key={mp.role} className={ROLE_COLORS[mp.role]} variant="outline">
                            {mp.role.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Permission Editor</CardTitle>
              <CardDescription>
                Customize permissions for each role and module
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Permission Editor</h3>
                <p className="text-muted-foreground mb-4">
                  Click the button below to open the advanced permission editor
                </p>
                <Button onClick={handleOpenPermissionEditor}>
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Permissions
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* View Role Dialog */}
      <AlertDialog open={isViewRoleDialogOpen} onOpenChange={setIsViewRoleDialogOpen}>
        <AlertDialogContent className="max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Role Details: {selectedRoleForView?.name}</AlertDialogTitle>
            <AlertDialogDescription>
              Complete information about this role and its permissions
            </AlertDialogDescription>
          </AlertDialogHeader>

          {selectedRoleForView && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">Role Name</Label>
                  <p className="text-sm">{selectedRoleForView.name}</p>
                </div>
                <div>
                  <Label className="font-medium">Type</Label>
                  <p className="text-sm">{selectedRoleForView.isCustom ? 'Custom Role' : 'Predefined Role'}</p>
                </div>
                <div>
                  <Label className="font-medium">Modules</Label>
                  <p className="text-sm">{selectedRoleForView.moduleCount} modules</p>
                </div>
                <div>
                  <Label className="font-medium">Permissions</Label>
                  <p className="text-sm">{selectedRoleForView.permissionCount} permissions</p>
                </div>
              </div>

              {selectedRoleForView.description && (
                <div>
                  <Label className="font-medium">Description</Label>
                  <p className="text-sm text-muted-foreground">{selectedRoleForView.description}</p>
                </div>
              )}

              <div>
                <Label className="font-medium">Created</Label>
                <p className="text-sm">{new Date(selectedRoleForView.createdAt).toLocaleDateString()}</p>
              </div>
            </div>
          )}

          <AlertDialogFooter>
            <AlertDialogCancel>Close</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Role Dialog */}
      <AlertDialog open={isEditRoleDialogOpen} onOpenChange={setIsEditRoleDialogOpen}>
        <AlertDialogContent className="max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Edit Role: {selectedRoleForView?.name}</AlertDialogTitle>
            <AlertDialogDescription>
              Modify role details and permissions
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4 py-4">
            <div className="text-center py-8">
              <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Role Editor</h3>
              <p className="text-muted-foreground mb-4">
                Role editing functionality will be implemented here
              </p>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction>Save Changes</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Permission Editor Dialog */}
      <AlertDialog open={isPermissionEditorOpen} onOpenChange={setIsPermissionEditorOpen}>
        <AlertDialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <AlertDialogHeader>
            <AlertDialogTitle>Permission Editor</AlertDialogTitle>
            <AlertDialogDescription>
              Configure permissions for roles and modules
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4 py-4">
            {Object.keys(editingPermissions).length > 0 ? (
              <div className="space-y-4">
                {Object.values(editingPermissions).map((item: any) => (
                  <Card key={`${item.role}_${item.module}`}>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {item.role.replace('_', ' ')} - {MODULE_INFO[item.module]?.name || item.module}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-4 gap-2">
                        {allPermissions.map(permission => (
                          <div key={permission} className="flex items-center space-x-2">
                            <Checkbox
                              checked={item.permissions.includes(permission)}
                              onCheckedChange={(checked) => {
                                const key = `${item.role}_${item.module}`
                                setEditingPermissions(prev => ({
                                  ...prev,
                                  [key]: {
                                    ...prev[key],
                                    permissions: checked
                                      ? [...prev[key].permissions, permission]
                                      : prev[key].permissions.filter((p: Permission) => p !== permission)
                                  }
                                }))
                              }}
                              disabled={item.role === 'FOUNDER'}
                            />
                            <Label className="text-sm">{permission}</Label>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Permissions Selected</h3>
                <p className="text-muted-foreground">
                  Select a role-module combination from the matrix to edit permissions
                </p>
              </div>
            )}
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSavePermissions}>
              <Save className="h-4 w-4 mr-2" />
              Save Permissions
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
