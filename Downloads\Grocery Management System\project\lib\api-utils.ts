import { NextRequest, NextResponse } from 'next/server'
import { checkPermission } from './auth'
import { prisma } from './prisma'

// Standard API response wrapper
export function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status })
}

// Error response wrapper
export function createErrorResponse(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status })
}

// Success response wrapper
export function createSuccessResponse(data: any, message?: string) {
  return NextResponse.json({
    success: true,
    data,
    message
  })
}

// Paginated response wrapper
export function createPaginatedResponse(
  data: any[],
  page: number,
  limit: number,
  total: number
) {
  return NextResponse.json({
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1
    }
  })
}

// Permission-based API handler wrapper
export function withPermission(
  module: string,
  permission: string,
  handler: (request: NextRequest, user: any, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any) => {
    try {
      const permissionCheck = await checkPermission(request, module, permission)

      if (permissionCheck.error) {
        return createErrorResponse(permissionCheck.error, permissionCheck.status)
      }

      return await handler(request, permissionCheck.user, context)
    } catch (error) {
      console.error(`API Error in ${module}:`, error)
      return createErrorResponse('Internal server error', 500)
    }
  }
}

// Audit log helper
export async function createAuditLog(
  action: string,
  module: string,
  details: string,
  userId: string,
  storeId: string
) {
  try {
    await prisma.auditLog.create({
      data: {
        action,
        module,
        details,
        userId,
        storeId
      }
    })
  } catch (error) {
    console.error('Failed to create audit log:', error)
  }
}

// Pagination helper
export function getPaginationParams(request: NextRequest) {
  const url = new URL(request.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '10')
  const search = url.searchParams.get('search') || ''
  const sortBy = url.searchParams.get('sortBy') || 'createdAt'
  const sortOrder = url.searchParams.get('sortOrder') || 'desc'

  return {
    page: Math.max(1, page),
    limit: Math.min(100, Math.max(1, limit)),
    skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),
    search,
    sortBy,
    sortOrder: sortOrder === 'asc' ? 'asc' : 'desc'
  }
}

// Search filter helper
export function buildSearchFilter(search: string, fields: string[]) {
  if (!search) return {}

  return {
    OR: fields.map(field => ({
      [field]: {
        contains: search,
        mode: 'insensitive'
      }
    }))
  }
}

// Date range filter helper
export function buildDateRangeFilter(
  startDate?: string,
  endDate?: string,
  field: string = 'createdAt'
) {
  const filter: any = {}

  if (startDate) {
    filter[field] = { ...filter[field], gte: new Date(startDate) }
  }

  if (endDate) {
    const end = new Date(endDate)
    end.setHours(23, 59, 59, 999)
    filter[field] = { ...filter[field], lte: end }
  }

  return Object.keys(filter).length > 0 ? filter : {}
}

// Validation helper
export function validateRequiredFields(data: any, fields: string[]) {
  const missing = fields.filter(field => !data[field])

  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`)
  }
}

// Store validation helper
export function validateStoreAccess(user: any) {
  if (!user.storeId) {
    throw new Error('User not associated with any store')
  }
  return user.storeId
}
