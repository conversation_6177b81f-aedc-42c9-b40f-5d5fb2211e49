'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ROLE_PERMISSIONS } from '@/lib/permissions'
import { Role, Module } from '@/lib/types'

export default function PermissionsTestPage() {
  const roles: Role[] = ['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']
  
  const getAllModules = (): Module[] => {
    const modules = new Set<Module>()
    ROLE_PERMISSIONS.forEach(rp => modules.add(rp.module))
    return Array.from(modules).sort()
  }

  const getPermissionsForRole = (role: Role): { [key in Module]?: string[] } => {
    const permissions: { [key in Module]?: string[] } = {}
    ROLE_PERMISSIONS
      .filter(rp => rp.role === role)
      .forEach(rp => {
        permissions[rp.module] = rp.permissions
      })
    return permissions
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'MANAGE': return 'bg-red-100 text-red-800'
      case 'DELETE': return 'bg-orange-100 text-orange-800'
      case 'CREATE': return 'bg-green-100 text-green-800'
      case 'UPDATE': return 'bg-blue-100 text-blue-800'
      case 'READ': return 'bg-gray-100 text-gray-800'
      case 'EXPORT': return 'bg-purple-100 text-purple-800'
      case 'APPROVE': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const allModules = getAllModules()

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Permission Analysis</h1>
        <p className="text-gray-600 mt-1">Complete overview of role-based permissions</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        {roles.map(role => {
          const rolePermissions = getPermissionsForRole(role)
          const moduleCount = Object.keys(rolePermissions).length
          const totalPermissions = Object.values(rolePermissions).flat().length
          
          return (
            <Card key={role}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{role.replace('_', ' ')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{moduleCount}</div>
                <p className="text-xs text-muted-foreground">modules</p>
                <div className="text-lg font-semibold text-blue-600">{totalPermissions}</div>
                <p className="text-xs text-muted-foreground">permissions</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Detailed Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Matrix</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 font-medium">Module</th>
                  {roles.map(role => (
                    <th key={role} className="text-left p-3 font-medium min-w-48">
                      {role.replace('_', ' ')}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {allModules.map(module => (
                  <tr key={module} className="border-b hover:bg-gray-50">
                    <td className="p-3 font-medium">{module}</td>
                    {roles.map(role => {
                      const rolePermissions = getPermissionsForRole(role)
                      const permissions = rolePermissions[module] || []
                      
                      return (
                        <td key={`${role}-${module}`} className="p-3">
                          {permissions.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {permissions.map(permission => (
                                <Badge 
                                  key={permission} 
                                  className={`text-xs ${getPermissionColor(permission)}`}
                                >
                                  {permission}
                                </Badge>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400 text-sm">No Access</span>
                          )}
                        </td>
                      )
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Permission Legend */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Permission Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Badge className="bg-red-100 text-red-800">MANAGE</Badge>
              <span className="text-sm">Full control over module</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-orange-100 text-orange-800">DELETE</Badge>
              <span className="text-sm">Can delete records</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-green-100 text-green-800">CREATE</Badge>
              <span className="text-sm">Can create new records</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-blue-100 text-blue-800">UPDATE</Badge>
              <span className="text-sm">Can modify existing records</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-gray-100 text-gray-800">READ</Badge>
              <span className="text-sm">Can view records</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-purple-100 text-purple-800">EXPORT</Badge>
              <span className="text-sm">Can export data</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className="bg-yellow-100 text-yellow-800">APPROVE</Badge>
              <span className="text-sm">Can approve requests</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role Descriptions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Role Descriptions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-red-600">FOUNDER</h3>
              <p className="text-sm text-gray-600">Complete system access with all permissions including MANAGE rights. Can control all aspects of the business.</p>
            </div>
            <div>
              <h3 className="font-semibold text-orange-600">SUPER_ADMIN</h3>
              <p className="text-sm text-gray-600">High-level access with most permissions but limited MANAGE rights. Cannot modify core system settings.</p>
            </div>
            <div>
              <h3 className="font-semibold text-blue-600">ADMIN</h3>
              <p className="text-sm text-gray-600">Operational access focused on daily business operations. Can manage products, sales, customers, and suppliers.</p>
            </div>
            <div>
              <h3 className="font-semibold text-green-600">STAFF</h3>
              <p className="text-sm text-gray-600">Limited access for front-line employees. Can handle sales, basic customer management, and view inventory.</p>
            </div>
            <div>
              <h3 className="font-semibold text-purple-600">DISTRIBUTOR</h3>
              <p className="text-sm text-gray-600">External partner access limited to purchase-related activities and basic dashboard viewing.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
